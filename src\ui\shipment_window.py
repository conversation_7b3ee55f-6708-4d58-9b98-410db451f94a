"""
نافذة إدارة الشحنات لبرنامج احتساب الجمارك
Shipment management window for customs calculation program
"""

import os
import sys
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QLineEdit, QTextEdit, QComboBox, QSpinBox,
                               QDoubleSpinBox, QDateEdit, QPushButton, QGroupBox,
                               QMessageBox, QTableWidget, QTableWidgetItem,
                               QHeaderView, QAbstractItemView, QFrame)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QFont

from src.utils.helpers import ArabicTextHelper, ValidationHelper, CustomsCalculator, TrackingNumberGenerator, NumberFormatter, DateTimeHelper
from src.database.database import DatabaseManager, Shipment, Customer, GoodsType

class ShipmentDialog(QDialog):
    """نافذة إضافة/تعديل الشحنة"""
    
    shipment_saved = Signal()  # إشارة عند حفظ الشحنة
    
    def __init__(self, parent=None, shipment_id=None):
        super().__init__(parent)
        self.shipment_id = shipment_id
        self.db_manager = DatabaseManager()
        self.is_edit_mode = shipment_id is not None
        
        self.init_ui()
        self.setup_arabic_support()
        self.load_data()
        
        if self.is_edit_mode:
            self.load_shipment_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        title = "تعديل الشحنة" if self.is_edit_mode else "إضافة شحنة جديدة"
        self.setWindowTitle(ArabicTextHelper.reshape_arabic_text(title))
        self.setModal(True)
        self.resize(800, 700)
        
        # إعداد الخط العربي
        arabic_font = QFont("Segoe UI", 10)
        self.setFont(arabic_font)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # معلومات الشحنة الأساسية
        self.create_basic_info_group(main_layout)
        
        # معلومات المرسل والمستقبل
        self.create_parties_group(main_layout)
        
        # معلومات البضاعة
        self.create_goods_group(main_layout)
        
        # معلومات الشحن والحسابات
        self.create_shipping_calculations_group(main_layout)
        
        # أزرار الحفظ والإلغاء
        self.create_buttons(main_layout)
        
        # تطبيق الستايل
        self.apply_styles()
    
    def setup_arabic_support(self):
        """إعداد دعم اللغة العربية"""
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    def create_basic_info_group(self, parent_layout):
        """إنشاء مجموعة المعلومات الأساسية"""
        group = QGroupBox(ArabicTextHelper.reshape_arabic_text("المعلومات الأساسية"))
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # رقم التتبع
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("رقم التتبع:")), 0, 0)
        self.tracking_edit = QLineEdit()
        self.tracking_edit.setPlaceholderText("سيتم توليده تلقائياً")
        layout.addWidget(self.tracking_edit, 0, 1)
        
        # تاريخ الشحن
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("تاريخ الشحن:")), 0, 2)
        self.shipment_date = QDateEdit()
        self.shipment_date.setDate(QDate.currentDate())
        self.shipment_date.setCalendarPopup(True)
        layout.addWidget(self.shipment_date, 0, 3)
        
        # تاريخ الوصول
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("تاريخ الوصول:")), 1, 0)
        self.arrival_date = QDateEdit()
        self.arrival_date.setCalendarPopup(True)
        layout.addWidget(self.arrival_date, 1, 1)
        
        # حالة الشحنة
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("حالة الشحنة:")), 1, 2)
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            ArabicTextHelper.reshape_arabic_text("معلقة"),
            ArabicTextHelper.reshape_arabic_text("في الطريق"),
            ArabicTextHelper.reshape_arabic_text("وصلت"),
            ArabicTextHelper.reshape_arabic_text("تم التسليم"),
            ArabicTextHelper.reshape_arabic_text("ملغية")
        ])
        layout.addWidget(self.status_combo, 1, 3)
        
        parent_layout.addWidget(group)
    
    def create_parties_group(self, parent_layout):
        """إنشاء مجموعة معلومات المرسل والمستقبل"""
        group = QGroupBox(ArabicTextHelper.reshape_arabic_text("معلومات المرسل والمستقبل"))
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # المرسل
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("المرسل:")), 0, 0)
        self.sender_combo = QComboBox()
        self.sender_combo.setEditable(True)
        layout.addWidget(self.sender_combo, 0, 1)
        
        # المستقبل
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("المستقبل:")), 0, 2)
        self.receiver_combo = QComboBox()
        self.receiver_combo.setEditable(True)
        layout.addWidget(self.receiver_combo, 0, 3)
        
        # بلد المنشأ
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("بلد المنشأ:")), 1, 0)
        self.origin_edit = QLineEdit()
        layout.addWidget(self.origin_edit, 1, 1)
        
        # بلد الوجهة
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("بلد الوجهة:")), 1, 2)
        self.destination_edit = QLineEdit()
        layout.addWidget(self.destination_edit, 1, 3)
        
        parent_layout.addWidget(group)
    
    def create_goods_group(self, parent_layout):
        """إنشاء مجموعة معلومات البضاعة"""
        group = QGroupBox(ArabicTextHelper.reshape_arabic_text("معلومات البضاعة"))
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # نوع البضاعة
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("نوع البضاعة:")), 0, 0)
        self.goods_type_combo = QComboBox()
        self.goods_type_combo.currentTextChanged.connect(self.on_goods_type_changed)
        layout.addWidget(self.goods_type_combo, 0, 1)
        
        # وصف البضاعة
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("وصف البضاعة:")), 0, 2)
        self.goods_description_edit = QTextEdit()
        self.goods_description_edit.setMaximumHeight(80)
        layout.addWidget(self.goods_description_edit, 0, 3, 2, 1)
        
        # الكمية
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("الكمية:")), 1, 0)
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(9999)
        self.quantity_spin.setValue(1)
        layout.addWidget(self.quantity_spin, 1, 1)
        
        # الوزن
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("الوزن (كيلو):")), 1, 2)
        self.weight_spin = QDoubleSpinBox()
        self.weight_spin.setMinimum(0.1)
        self.weight_spin.setMaximum(9999.99)
        self.weight_spin.setDecimals(2)
        self.weight_spin.setSuffix(" كيلو")
        layout.addWidget(self.weight_spin, 1, 3)
        
        # قيمة البضاعة
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("قيمة البضاعة:")), 2, 0)
        self.value_spin = QDoubleSpinBox()
        self.value_spin.setMinimum(0.01)
        self.value_spin.setMaximum(999999.99)
        self.value_spin.setDecimals(2)
        self.value_spin.valueChanged.connect(self.calculate_fees)
        layout.addWidget(self.value_spin, 2, 1)
        
        # العملة
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("العملة:")), 2, 2)
        self.currency_combo = QComboBox()
        self.currency_combo.addItems(["USD", "EUR", "SAR", "AED", "EGP"])
        layout.addWidget(self.currency_combo, 2, 3)
        
        parent_layout.addWidget(group)
    
    def create_shipping_calculations_group(self, parent_layout):
        """إنشاء مجموعة معلومات الشحن والحسابات"""
        group = QGroupBox(ArabicTextHelper.reshape_arabic_text("الشحن والحسابات"))
        layout = QGridLayout(group)
        layout.setSpacing(10)
        
        # طريقة الشحن
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("طريقة الشحن:")), 0, 0)
        self.shipping_method_combo = QComboBox()
        self.shipping_method_combo.addItems([
            ArabicTextHelper.reshape_arabic_text("جوي"),
            ArabicTextHelper.reshape_arabic_text("بحري"),
            ArabicTextHelper.reshape_arabic_text("بري"),
            ArabicTextHelper.reshape_arabic_text("سريع")
        ])
        layout.addWidget(self.shipping_method_combo, 0, 1)
        
        # معدل الجمارك
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("معدل الجمارك (%):")), 0, 2)
        self.customs_rate_spin = QDoubleSpinBox()
        self.customs_rate_spin.setMinimum(0.0)
        self.customs_rate_spin.setMaximum(100.0)
        self.customs_rate_spin.setDecimals(2)
        self.customs_rate_spin.setSuffix("%")
        self.customs_rate_spin.valueChanged.connect(self.calculate_fees)
        layout.addWidget(self.customs_rate_spin, 0, 3)
        
        # معدل الضريبة
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("معدل الضريبة (%):")), 1, 0)
        self.tax_rate_spin = QDoubleSpinBox()
        self.tax_rate_spin.setMinimum(0.0)
        self.tax_rate_spin.setMaximum(100.0)
        self.tax_rate_spin.setDecimals(2)
        self.tax_rate_spin.setSuffix("%")
        self.tax_rate_spin.valueChanged.connect(self.calculate_fees)
        layout.addWidget(self.tax_rate_spin, 1, 1)
        
        # مبلغ الجمارك
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("مبلغ الجمارك:")), 1, 2)
        self.customs_amount_label = QLabel("0.00")
        self.customs_amount_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        layout.addWidget(self.customs_amount_label, 1, 3)
        
        # مبلغ الضريبة
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("مبلغ الضريبة:")), 2, 0)
        self.tax_amount_label = QLabel("0.00")
        self.tax_amount_label.setStyleSheet("font-weight: bold; color: #e74c3c;")
        layout.addWidget(self.tax_amount_label, 2, 1)
        
        # إجمالي الرسوم
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("إجمالي الرسوم:")), 2, 2)
        self.total_fees_label = QLabel("0.00")
        self.total_fees_label.setStyleSheet("font-weight: bold; color: #c0392b; font-size: 14px;")
        layout.addWidget(self.total_fees_label, 2, 3)
        
        # ملاحظات
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("ملاحظات:")), 3, 0)
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        layout.addWidget(self.notes_edit, 3, 1, 1, 3)
        
        parent_layout.addWidget(group)
    
    def create_buttons(self, parent_layout):
        """إنشاء أزرار الحفظ والإلغاء"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        # زر الحفظ
        save_text = "تحديث" if self.is_edit_mode else "حفظ"
        self.save_btn = QPushButton(ArabicTextHelper.reshape_arabic_text(save_text))
        self.save_btn.clicked.connect(self.save_shipment)
        self.save_btn.setDefault(True)
        buttons_layout.addWidget(self.save_btn)
        
        # زر الإلغاء
        self.cancel_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("إلغاء"))
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)
        
        # زر حساب الرسوم
        self.calculate_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("حساب الرسوم"))
        self.calculate_btn.clicked.connect(self.calculate_fees)
        buttons_layout.addWidget(self.calculate_btn)
        
        buttons_layout.addStretch()
        parent_layout.addLayout(buttons_layout)
    
    def apply_styles(self):
        """تطبيق الستايلات"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
            
            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                background-color: white;
            }
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border: 2px solid #3498db;
            }
            
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            
            QPushButton:hover {
                background-color: #2980b9;
            }
            
            QPushButton:pressed {
                background-color: #21618c;
            }
            
            QLabel {
                color: #2c3e50;
            }
        """)
    
    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        session = self.db_manager.get_session()
        try:
            # تحميل العملاء
            customers = session.query(Customer).all()
            for customer in customers:
                display_name = f"{customer.name} - {customer.company or 'بدون شركة'}"
                self.sender_combo.addItem(display_name, customer.id)
                self.receiver_combo.addItem(display_name, customer.id)
            
            # تحميل أنواع البضائع
            goods_types = session.query(GoodsType).filter(GoodsType.is_active == True).all()
            for goods_type in goods_types:
                self.goods_type_combo.addItem(goods_type.name, goods_type.id)
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل البيانات: {e}")
        finally:
            session.close()
    
    def on_goods_type_changed(self):
        """عند تغيير نوع البضاعة"""
        current_data = self.goods_type_combo.currentData()
        if current_data:
            session = self.db_manager.get_session()
            try:
                goods_type = session.query(GoodsType).get(current_data)
                if goods_type:
                    self.customs_rate_spin.setValue(goods_type.customs_rate)
                    self.tax_rate_spin.setValue(goods_type.tax_rate)
                    self.calculate_fees()
            except Exception as e:
                print(f"خطأ في تحميل معدلات البضاعة: {e}")
            finally:
                session.close()
    
    def calculate_fees(self):
        """حساب الرسوم"""
        try:
            value = self.value_spin.value()
            customs_rate = self.customs_rate_spin.value()
            tax_rate = self.tax_rate_spin.value()
            
            customs_amount = CustomsCalculator.calculate_customs(value, customs_rate)
            tax_amount = CustomsCalculator.calculate_tax(value, tax_rate)
            total_fees = customs_amount + tax_amount
            
            currency = self.currency_combo.currentText()
            
            self.customs_amount_label.setText(f"{customs_amount:.2f} {currency}")
            self.tax_amount_label.setText(f"{tax_amount:.2f} {currency}")
            self.total_fees_label.setText(f"{total_fees:.2f} {currency}")
            
        except Exception as e:
            print(f"خطأ في حساب الرسوم: {e}")
    
    def load_shipment_data(self):
        """تحميل بيانات الشحنة للتعديل"""
        if not self.shipment_id:
            return
        
        session = self.db_manager.get_session()
        try:
            shipment = session.query(Shipment).get(self.shipment_id)
            if shipment:
                # تحميل البيانات الأساسية
                self.tracking_edit.setText(shipment.tracking_number or "")
                
                if shipment.shipment_date:
                    self.shipment_date.setDate(QDate.fromString(shipment.shipment_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))
                
                if shipment.arrival_date:
                    self.arrival_date.setDate(QDate.fromString(shipment.arrival_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))
                
                # تحديد الحالة
                status_map = {
                    'pending': 0, 'in_transit': 1, 'arrived': 2, 'delivered': 3, 'cancelled': 4
                }
                self.status_combo.setCurrentIndex(status_map.get(shipment.status, 0))
                
                # تحميل بيانات الأطراف
                if shipment.sender_id:
                    for i in range(self.sender_combo.count()):
                        if self.sender_combo.itemData(i) == shipment.sender_id:
                            self.sender_combo.setCurrentIndex(i)
                            break
                
                if shipment.receiver_id:
                    for i in range(self.receiver_combo.count()):
                        if self.receiver_combo.itemData(i) == shipment.receiver_id:
                            self.receiver_combo.setCurrentIndex(i)
                            break
                
                # تحميل بيانات البضاعة
                self.origin_edit.setText(shipment.origin_country or "")
                self.destination_edit.setText(shipment.destination_country or "")
                self.goods_description_edit.setPlainText(shipment.goods_description or "")
                self.quantity_spin.setValue(shipment.quantity or 1)
                self.weight_spin.setValue(shipment.weight or 0.0)
                self.value_spin.setValue(shipment.value or 0.0)
                
                # تحديد العملة
                currency_index = self.currency_combo.findText(shipment.currency or "USD")
                if currency_index >= 0:
                    self.currency_combo.setCurrentIndex(currency_index)
                
                # تحديد نوع البضاعة
                if shipment.goods_type_id:
                    for i in range(self.goods_type_combo.count()):
                        if self.goods_type_combo.itemData(i) == shipment.goods_type_id:
                            self.goods_type_combo.setCurrentIndex(i)
                            break
                
                # تحميل بيانات الشحن
                shipping_methods = ["جوي", "بحري", "بري", "سريع"]
                method_index = 0
                if shipment.shipping_method:
                    for i, method in enumerate(shipping_methods):
                        if method in shipment.shipping_method:
                            method_index = i
                            break
                self.shipping_method_combo.setCurrentIndex(method_index)
                
                self.notes_edit.setPlainText(shipment.notes or "")
                
                # حساب الرسوم
                self.calculate_fees()
                
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل بيانات الشحنة: {e}")
        finally:
            session.close()
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        errors = []
        
        # التحقق من رقم التتبع
        tracking = self.tracking_edit.text().strip()
        if not tracking:
            errors.append("رقم التتبع مطلوب")
        
        # التحقق من قيمة البضاعة
        if self.value_spin.value() <= 0:
            errors.append("قيمة البضاعة يجب أن تكون أكبر من صفر")
        
        # التحقق من الوزن
        if self.weight_spin.value() <= 0:
            errors.append("وزن البضاعة يجب أن يكون أكبر من صفر")
        
        return errors
    
    def save_shipment(self):
        """حفظ الشحنة"""
        # التحقق من صحة البيانات
        errors = self.validate_data()
        if errors:
            error_message = "\n".join(errors)
            QMessageBox.warning(self, "خطأ في البيانات", ArabicTextHelper.reshape_arabic_text(error_message))
            return
        
        session = self.db_manager.get_session()
        try:
            if self.is_edit_mode:
                shipment = session.query(Shipment).get(self.shipment_id)
                if not shipment:
                    QMessageBox.warning(self, "خطأ", "لم يتم العثور على الشحنة")
                    return
            else:
                shipment = Shipment()
                # توليد رقم تتبع إذا لم يتم إدخاله
                tracking = self.tracking_edit.text().strip()
                if not tracking:
                    tracking = TrackingNumberGenerator.generate_tracking_number()
                    self.tracking_edit.setText(tracking)
                shipment.tracking_number = tracking
            
            # حفظ البيانات الأساسية
            shipment.tracking_number = self.tracking_edit.text().strip()
            shipment.shipment_date = self.shipment_date.date().toPython()
            
            if self.arrival_date.date() != QDate.currentDate():
                shipment.arrival_date = self.arrival_date.date().toPython()
            
            # حفظ الحالة
            status_map = ['pending', 'in_transit', 'arrived', 'delivered', 'cancelled']
            shipment.status = status_map[self.status_combo.currentIndex()]
            
            # حفظ معرفات الأطراف
            shipment.sender_id = self.sender_combo.currentData()
            shipment.receiver_id = self.receiver_combo.currentData()
            
            # حفظ بيانات البضاعة
            shipment.origin_country = self.origin_edit.text().strip()
            shipment.destination_country = self.destination_edit.text().strip()
            shipment.goods_type_id = self.goods_type_combo.currentData()
            shipment.goods_description = self.goods_description_edit.toPlainText().strip()
            shipment.quantity = self.quantity_spin.value()
            shipment.weight = self.weight_spin.value()
            shipment.value = self.value_spin.value()
            shipment.currency = self.currency_combo.currentText()
            
            # حفظ بيانات الشحن
            shipment.shipping_method = self.shipping_method_combo.currentText()
            
            # حساب وحفظ الرسوم
            customs_rate = self.customs_rate_spin.value()
            tax_rate = self.tax_rate_spin.value()
            shipment.customs_amount = CustomsCalculator.calculate_customs(shipment.value, customs_rate)
            shipment.tax_amount = CustomsCalculator.calculate_tax(shipment.value, tax_rate)
            shipment.total_fees = shipment.customs_amount + shipment.tax_amount
            
            shipment.notes = self.notes_edit.toPlainText().strip()
            
            if not self.is_edit_mode:
                session.add(shipment)
            
            session.commit()
            
            # إرسال إشارة النجاح
            self.shipment_saved.emit()
            
            success_message = "تم تحديث الشحنة بنجاح" if self.is_edit_mode else "تم حفظ الشحنة بنجاح"
            QMessageBox.information(self, "نجح", ArabicTextHelper.reshape_arabic_text(success_message))
            
            self.accept()
            
        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الشحنة: {e}")
        finally:
            session.close()


class ShipmentListWindow(QDialog):
    """نافذة عرض قائمة الشحنات"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.init_ui()
        self.setup_arabic_support()
        self.load_shipments()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle(ArabicTextHelper.reshape_arabic_text("إدارة الشحنات"))
        self.setModal(True)
        self.resize(1200, 700)

        # إعداد الخط العربي
        arabic_font = QFont("Segoe UI", 10)
        self.setFont(arabic_font)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # شريط البحث والأدوات
        self.create_toolbar(main_layout)

        # جدول الشحنات
        self.create_shipments_table(main_layout)

        # أزرار الإجراءات
        self.create_action_buttons(main_layout)

        # تطبيق الستايل
        self.apply_styles()

    def setup_arabic_support(self):
        """إعداد دعم اللغة العربية"""
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

    def create_toolbar(self, parent_layout):
        """إنشاء شريط البحث والأدوات"""
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setSpacing(10)

        # حقل البحث
        search_label = QLabel(ArabicTextHelper.reshape_arabic_text("البحث:"))
        toolbar_layout.addWidget(search_label)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث برقم التتبع أو اسم العميل...")
        self.search_edit.textChanged.connect(self.filter_shipments)
        toolbar_layout.addWidget(self.search_edit)

        # فلتر الحالة
        status_label = QLabel(ArabicTextHelper.reshape_arabic_text("الحالة:"))
        toolbar_layout.addWidget(status_label)

        self.status_filter = QComboBox()
        self.status_filter.addItems([
            ArabicTextHelper.reshape_arabic_text("جميع الحالات"),
            ArabicTextHelper.reshape_arabic_text("معلقة"),
            ArabicTextHelper.reshape_arabic_text("في الطريق"),
            ArabicTextHelper.reshape_arabic_text("وصلت"),
            ArabicTextHelper.reshape_arabic_text("تم التسليم"),
            ArabicTextHelper.reshape_arabic_text("ملغية")
        ])
        self.status_filter.currentTextChanged.connect(self.filter_shipments)
        toolbar_layout.addWidget(self.status_filter)

        # زر التحديث
        refresh_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("تحديث"))
        refresh_btn.clicked.connect(self.load_shipments)
        toolbar_layout.addWidget(refresh_btn)

        toolbar_layout.addStretch()
        parent_layout.addLayout(toolbar_layout)

    def create_shipments_table(self, parent_layout):
        """إنشاء جدول الشحنات"""
        self.shipments_table = QTableWidget()
        self.shipments_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.shipments_table.setAlternatingRowColors(True)
        self.shipments_table.setSortingEnabled(True)

        # تعيين أعمدة الجدول
        headers = [
            "رقم التتبع", "المرسل", "المستقبل", "نوع البضاعة",
            "القيمة", "الجمارك", "الضريبة", "إجمالي الرسوم",
            "تاريخ الشحن", "الحالة"
        ]

        self.shipments_table.setColumnCount(len(headers))
        self.shipments_table.setHorizontalHeaderLabels([ArabicTextHelper.reshape_arabic_text(h) for h in headers])

        # تعيين عرض الأعمدة
        header = self.shipments_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers)):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)

        # إعداد الأحداث
        self.shipments_table.doubleClicked.connect(self.edit_shipment)

        parent_layout.addWidget(self.shipments_table)

    def create_action_buttons(self, parent_layout):
        """إنشاء أزرار الإجراءات"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # زر إضافة شحنة
        add_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("إضافة شحنة"))
        add_btn.clicked.connect(self.add_shipment)
        buttons_layout.addWidget(add_btn)

        # زر تعديل
        edit_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("تعديل"))
        edit_btn.clicked.connect(self.edit_shipment)
        buttons_layout.addWidget(edit_btn)

        # زر حذف
        delete_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("حذف"))
        delete_btn.clicked.connect(self.delete_shipment)
        delete_btn.setStyleSheet("background-color: #e74c3c;")
        buttons_layout.addWidget(delete_btn)

        # زر طباعة
        print_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("طباعة"))
        print_btn.clicked.connect(self.print_shipment)
        buttons_layout.addWidget(print_btn)

        buttons_layout.addStretch()

        # زر إغلاق
        close_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("إغلاق"))
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)

        parent_layout.addLayout(buttons_layout)

    def apply_styles(self):
        """تطبيق الستايلات"""
        self.setStyleSheet("""
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                selection-color: white;
            }

            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }

            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }

            QLineEdit, QComboBox {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                background-color: white;
            }

            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }

            QPushButton:hover {
                background-color: #2980b9;
            }
        """)

    def load_shipments(self):
        """تحميل الشحنات من قاعدة البيانات"""
        session = self.db_manager.get_session()
        try:
            shipments = session.query(Shipment).order_by(Shipment.created_at.desc()).all()
            self.populate_table(shipments)
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل الشحنات: {e}")
        finally:
            session.close()

    def populate_table(self, shipments):
        """ملء الجدول بالشحنات"""
        self.shipments_table.setRowCount(len(shipments))

        for row, shipment in enumerate(shipments):
            # رقم التتبع
            self.shipments_table.setItem(row, 0, QTableWidgetItem(shipment.tracking_number or ""))

            # المرسل
            sender_name = shipment.sender.name if shipment.sender else "غير محدد"
            self.shipments_table.setItem(row, 1, QTableWidgetItem(sender_name))

            # المستقبل
            receiver_name = shipment.receiver.name if shipment.receiver else "غير محدد"
            self.shipments_table.setItem(row, 2, QTableWidgetItem(receiver_name))

            # نوع البضاعة
            goods_type_name = shipment.goods_type.name if shipment.goods_type else "غير محدد"
            self.shipments_table.setItem(row, 3, QTableWidgetItem(goods_type_name))

            # القيمة
            value_text = NumberFormatter.format_currency(shipment.value or 0, shipment.currency or "USD")
            self.shipments_table.setItem(row, 4, QTableWidgetItem(value_text))

            # الجمارك
            customs_text = NumberFormatter.format_currency(shipment.customs_amount or 0, shipment.currency or "USD")
            self.shipments_table.setItem(row, 5, QTableWidgetItem(customs_text))

            # الضريبة
            tax_text = NumberFormatter.format_currency(shipment.tax_amount or 0, shipment.currency or "USD")
            self.shipments_table.setItem(row, 6, QTableWidgetItem(tax_text))

            # إجمالي الرسوم
            total_text = NumberFormatter.format_currency(shipment.total_fees or 0, shipment.currency or "USD")
            self.shipments_table.setItem(row, 7, QTableWidgetItem(total_text))

            # تاريخ الشحن
            date_text = DateTimeHelper.format_date(shipment.shipment_date)
            self.shipments_table.setItem(row, 8, QTableWidgetItem(date_text))

            # الحالة
            status_map = {
                'pending': 'معلقة',
                'in_transit': 'في الطريق',
                'arrived': 'وصلت',
                'delivered': 'تم التسليم',
                'cancelled': 'ملغية'
            }
            status_text = status_map.get(shipment.status, shipment.status)
            self.shipments_table.setItem(row, 9, QTableWidgetItem(ArabicTextHelper.reshape_arabic_text(status_text)))

            # حفظ معرف الشحنة في البيانات
            self.shipments_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, shipment.id)

    def filter_shipments(self):
        """فلترة الشحنات حسب البحث والحالة"""
        search_text = self.search_edit.text().lower()
        status_filter = self.status_filter.currentText()

        for row in range(self.shipments_table.rowCount()):
            show_row = True

            # فلترة البحث
            if search_text:
                tracking = self.shipments_table.item(row, 0).text().lower()
                sender = self.shipments_table.item(row, 1).text().lower()
                receiver = self.shipments_table.item(row, 2).text().lower()

                if not (search_text in tracking or search_text in sender or search_text in receiver):
                    show_row = False

            # فلترة الحالة
            if status_filter != ArabicTextHelper.reshape_arabic_text("جميع الحالات"):
                row_status = self.shipments_table.item(row, 9).text()
                if row_status != status_filter:
                    show_row = False

            self.shipments_table.setRowHidden(row, not show_row)

    def get_selected_shipment_id(self):
        """الحصول على معرف الشحنة المحددة"""
        current_row = self.shipments_table.currentRow()
        if current_row >= 0:
            item = self.shipments_table.item(current_row, 0)
            if item:
                return item.data(Qt.ItemDataRole.UserRole)
        return None

    def add_shipment(self):
        """إضافة شحنة جديدة"""
        dialog = ShipmentDialog(self)
        dialog.shipment_saved.connect(self.load_shipments)
        dialog.exec()

    def edit_shipment(self):
        """تعديل الشحنة المحددة"""
        shipment_id = self.get_selected_shipment_id()
        if shipment_id:
            dialog = ShipmentDialog(self, shipment_id)
            dialog.shipment_saved.connect(self.load_shipments)
            dialog.exec()
        else:
            QMessageBox.information(self, "تنبيه", ArabicTextHelper.reshape_arabic_text("يرجى تحديد شحنة للتعديل"))

    def delete_shipment(self):
        """حذف الشحنة المحددة"""
        shipment_id = self.get_selected_shipment_id()
        if not shipment_id:
            QMessageBox.information(self, "تنبيه", ArabicTextHelper.reshape_arabic_text("يرجى تحديد شحنة للحذف"))
            return

        # تأكيد الحذف
        reply = QMessageBox.question(
            self,
            ArabicTextHelper.reshape_arabic_text("تأكيد الحذف"),
            ArabicTextHelper.reshape_arabic_text("هل أنت متأكد من حذف هذه الشحنة؟"),
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            session = self.db_manager.get_session()
            try:
                shipment = session.query(Shipment).get(shipment_id)
                if shipment:
                    session.delete(shipment)
                    session.commit()
                    QMessageBox.information(self, "نجح", ArabicTextHelper.reshape_arabic_text("تم حذف الشحنة بنجاح"))
                    self.load_shipments()
                else:
                    QMessageBox.warning(self, "خطأ", "لم يتم العثور على الشحنة")
            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, "خطأ", f"فشل في حذف الشحنة: {e}")
            finally:
                session.close()

    def print_shipment(self):
        """طباعة الشحنة المحددة"""
        shipment_id = self.get_selected_shipment_id()
        if shipment_id:
            QMessageBox.information(self, "طباعة", "سيتم إضافة ميزة الطباعة قريباً")
        else:
            QMessageBox.information(self, "تنبيه", ArabicTextHelper.reshape_arabic_text("يرجى تحديد شحنة للطباعة"))
