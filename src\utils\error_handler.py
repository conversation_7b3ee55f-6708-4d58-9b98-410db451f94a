"""
معالج الأخطاء - Error handler
يوفر معالجة موحدة للأخطاء مع رسائل واضحة باللغة العربية
"""

import logging
import traceback
from datetime import datetime
from PySide6.QtWidgets import QMessageBox
from PySide6.QtCore import QObject, Signal
from .helpers import ArabicTextHelper

class ErrorHandler(QObject):
    """معالج الأخطاء - Error handler"""
    
    error_occurred = Signal(str, str)  # نوع الخطأ، رسالة الخطأ
    
    def __init__(self):
        super().__init__()
        self.setup_logging()
    
    def setup_logging(self):
        """إعداد نظام التسجيل"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('customs_calculator.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def handle_database_error(self, error, operation="عملية قاعدة البيانات"):
        """معالجة أخطاء قاعدة البيانات"""
        error_msg = self._get_database_error_message(error)
        user_msg = f"حدث خطأ في {operation}:\n{error_msg}"
        
        self.logger.error(f"Database error in {operation}: {str(error)}")
        self.logger.error(traceback.format_exc())
        
        self.error_occurred.emit("database", user_msg)
        return user_msg
    
    def handle_validation_error(self, field_name, error_type="مطلوب"):
        """معالجة أخطاء التحقق من صحة البيانات"""
        error_messages = {
            "مطلوب": f"الحقل '{field_name}' مطلوب",
            "تنسيق خاطئ": f"تنسيق الحقل '{field_name}' غير صحيح",
            "قيمة غير صالحة": f"قيمة الحقل '{field_name}' غير صالحة",
            "طويل جداً": f"الحقل '{field_name}' طويل جداً",
            "قصير جداً": f"الحقل '{field_name}' قصير جداً",
            "رقم غير صالح": f"الحقل '{field_name}' يجب أن يكون رقماً صالحاً",
            "تاريخ غير صالح": f"الحقل '{field_name}' يجب أن يكون تاريخاً صالحاً"
        }
        
        user_msg = error_messages.get(error_type, f"خطأ في الحقل '{field_name}'")
        
        self.logger.warning(f"Validation error: {user_msg}")
        self.error_occurred.emit("validation", user_msg)
        return user_msg
    
    def handle_file_error(self, error, operation="عملية الملف"):
        """معالجة أخطاء الملفات"""
        error_msg = self._get_file_error_message(error)
        user_msg = f"حدث خطأ في {operation}:\n{error_msg}"
        
        self.logger.error(f"File error in {operation}: {str(error)}")
        self.error_occurred.emit("file", user_msg)
        return user_msg
    
    def handle_network_error(self, error, operation="عملية الشبكة"):
        """معالجة أخطاء الشبكة"""
        error_msg = self._get_network_error_message(error)
        user_msg = f"حدث خطأ في {operation}:\n{error_msg}"
        
        self.logger.error(f"Network error in {operation}: {str(error)}")
        self.error_occurred.emit("network", user_msg)
        return user_msg
    
    def handle_general_error(self, error, operation="العملية"):
        """معالجة الأخطاء العامة"""
        user_msg = f"حدث خطأ غير متوقع في {operation}:\n{str(error)}"
        
        self.logger.error(f"General error in {operation}: {str(error)}")
        self.logger.error(traceback.format_exc())
        self.error_occurred.emit("general", user_msg)
        return user_msg
    
    def _get_database_error_message(self, error):
        """الحصول على رسالة خطأ قاعدة البيانات"""
        error_str = str(error).lower()
        
        if "unique constraint" in error_str:
            return "البيانات المدخلة موجودة مسبقاً"
        elif "foreign key" in error_str:
            return "خطأ في الربط بين البيانات"
        elif "not null" in error_str:
            return "حقل مطلوب لم يتم ملؤه"
        elif "database is locked" in error_str:
            return "قاعدة البيانات مقفلة، يرجى المحاولة لاحقاً"
        elif "no such table" in error_str:
            return "جدول غير موجود في قاعدة البيانات"
        elif "syntax error" in error_str:
            return "خطأ في بناء الاستعلام"
        else:
            return "خطأ في قاعدة البيانات"
    
    def _get_file_error_message(self, error):
        """الحصول على رسالة خطأ الملف"""
        error_str = str(error).lower()
        
        if "permission denied" in error_str:
            return "ليس لديك صلاحية للوصول إلى الملف"
        elif "file not found" in error_str:
            return "الملف غير موجود"
        elif "is a directory" in error_str:
            return "المسار المحدد هو مجلد وليس ملف"
        elif "disk full" in error_str:
            return "مساحة القرص ممتلئة"
        elif "invalid filename" in error_str:
            return "اسم الملف غير صالح"
        else:
            return "خطأ في التعامل مع الملف"
    
    def _get_network_error_message(self, error):
        """الحصول على رسالة خطأ الشبكة"""
        error_str = str(error).lower()
        
        if "connection refused" in error_str:
            return "تم رفض الاتصال"
        elif "timeout" in error_str:
            return "انتهت مهلة الاتصال"
        elif "host not found" in error_str:
            return "الخادم غير موجود"
        elif "network unreachable" in error_str:
            return "الشبكة غير متاحة"
        else:
            return "خطأ في الشبكة"
    
    def show_error_dialog(self, parent, title, message, error_type="عام"):
        """عرض نافذة خطأ"""
        # تشكيل النص العربي
        formatted_title = ArabicTextHelper.reshape_arabic_text(title)
        formatted_message = ArabicTextHelper.reshape_arabic_text(message)
        
        # تحديد نوع الرسالة
        if error_type == "تحذير":
            QMessageBox.warning(parent, formatted_title, formatted_message)
        elif error_type == "معلومات":
            QMessageBox.information(parent, formatted_title, formatted_message)
        elif error_type == "سؤال":
            return QMessageBox.question(parent, formatted_title, formatted_message)
        else:
            QMessageBox.critical(parent, formatted_title, formatted_message)
    
    def show_success_dialog(self, parent, title, message):
        """عرض نافذة نجاح العملية"""
        formatted_title = ArabicTextHelper.reshape_arabic_text(title)
        formatted_message = ArabicTextHelper.reshape_arabic_text(message)
        QMessageBox.information(parent, formatted_title, formatted_message)
    
    def log_info(self, message):
        """تسجيل معلومات"""
        self.logger.info(message)
    
    def log_warning(self, message):
        """تسجيل تحذير"""
        self.logger.warning(message)
    
    def log_error(self, message):
        """تسجيل خطأ"""
        self.logger.error(message)

# إنشاء مثيل عام لمعالج الأخطاء
error_handler = ErrorHandler()

def handle_exception(func):
    """ديكوريتر لمعالجة الاستثناءات"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            error_handler.handle_general_error(e, func.__name__)
            return None
    return wrapper

def validate_required_field(value, field_name):
    """التحقق من الحقول المطلوبة"""
    if not value or (isinstance(value, str) and not value.strip()):
        error_handler.handle_validation_error(field_name, "مطلوب")
        return False
    return True

def validate_number_field(value, field_name):
    """التحقق من الحقول الرقمية"""
    try:
        float(value)
        return True
    except (ValueError, TypeError):
        error_handler.handle_validation_error(field_name, "رقم غير صالح")
        return False

def validate_email_field(value, field_name):
    """التحقق من البريد الإلكتروني"""
    import re
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, value):
        error_handler.handle_validation_error(field_name, "تنسيق خاطئ")
        return False
    return True
