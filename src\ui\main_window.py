"""
النافذة الرئيسية لبرنامج احتساب الجمارك
Main window for customs calculation program
"""

import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                               QWidget, QPushButton, QLabel, QMenuBar, QMenu,
                               QStatusBar, QToolBar, QFrame, QGridLayout, QMessageBox)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QFont, QIcon, QPixmap, QPalette, QColor, QAction

import os
import sys

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from src.utils.helpers import ArabicTextHelper
from src.database.database import DatabaseManager
from src.utils.error_handler import error_handler, handle_exception

class MainWindow(QMainWindow):
    """النافذة الرئيسية - Main window"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()

        # تهيئة النوافذ الفرعية (lazy loading)
        self._shipment_dialog = None
        self._calculator_window = None
        self._reports_window = None
        self._settings_window = None

        self.init_ui()
        self.setup_arabic_support()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("برنامج احتساب الجمارك - Customs Calculator")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1000, 600)
        
        # إعداد الخط العربي
        arabic_font = QFont("Segoe UI", 11)
        arabic_font.setStyleHint(QFont.StyleHint.SansSerif)
        self.setFont(arabic_font)
        
        # إعداد الألوان والثيم
        self.setup_theme()
        
        # إنشاء القوائم
        self.create_menu_bar()
        
        # إنشاء شريط الأدوات
        self.create_toolbar()
        
        # إنشاء الواجهة المركزية
        self.create_central_widget()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
    def setup_theme(self):
        """إعداد الثيم والألوان"""
        # تطبيق ستايل مخصص
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            
            QMenuBar {
                background-color: #2c3e50;
                color: white;
                border: none;
                padding: 4px;
            }
            
            QMenuBar::item {
                background-color: transparent;
                padding: 8px 12px;
                margin: 2px;
                border-radius: 4px;
            }
            
            QMenuBar::item:selected {
                background-color: #34495e;
            }
            
            QMenu {
                background-color: white;
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 4px;
            }
            
            QMenu::item {
                padding: 8px 20px;
                border-radius: 4px;
            }
            
            QMenu::item:selected {
                background-color: #3498db;
                color: white;
            }
            
            QToolBar {
                background-color: #ecf0f1;
                border: none;
                spacing: 4px;
                padding: 8px;
            }
            
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 120px;
            }
            
            QPushButton:hover {
                background-color: #2980b9;
            }
            
            QPushButton:pressed {
                background-color: #21618c;
            }
            
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
            
            QFrame#dashboard_frame {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                margin: 10px;
            }
            
            QLabel#title_label {
                color: #2c3e50;
                font-size: 18px;
                font-weight: bold;
                padding: 10px;
            }
            
            QLabel#stats_label {
                color: #34495e;
                font-size: 14px;
                padding: 5px;
            }
            
            QStatusBar {
                background-color: #ecf0f1;
                border-top: 1px solid #bdc3c7;
                color: #2c3e50;
            }
        """)
    
    def setup_arabic_support(self):
        """إعداد دعم اللغة العربية"""
        # تعيين اتجاه التخطيط من اليمين إلى اليسار
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # تعيين محاذاة النص للعربية
        QApplication.instance().setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu(ArabicTextHelper.reshape_arabic_text("ملف"))
        
        new_action = QAction(ArabicTextHelper.reshape_arabic_text("جديد"), self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.new_file)
        file_menu.addAction(new_action)
        
        open_action = QAction(ArabicTextHelper.reshape_arabic_text("فتح"), self)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self.open_file)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction(ArabicTextHelper.reshape_arabic_text("خروج"), self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة الشحنات
        shipments_menu = menubar.addMenu(ArabicTextHelper.reshape_arabic_text("الشحنات"))
        
        add_shipment_action = QAction(ArabicTextHelper.reshape_arabic_text("إضافة شحنة"), self)
        add_shipment_action.triggered.connect(self.add_shipment)
        shipments_menu.addAction(add_shipment_action)
        
        view_shipments_action = QAction(ArabicTextHelper.reshape_arabic_text("عرض الشحنات"), self)
        view_shipments_action.triggered.connect(self.view_shipments)
        shipments_menu.addAction(view_shipments_action)
        
        # قائمة العملاء
        customers_menu = menubar.addMenu(ArabicTextHelper.reshape_arabic_text("العملاء"))
        
        add_customer_action = QAction(ArabicTextHelper.reshape_arabic_text("إضافة عميل"), self)
        add_customer_action.triggered.connect(self.add_customer)
        customers_menu.addAction(add_customer_action)
        
        view_customers_action = QAction(ArabicTextHelper.reshape_arabic_text("عرض العملاء"), self)
        view_customers_action.triggered.connect(self.view_customers)
        customers_menu.addAction(view_customers_action)
        
        # قائمة التقارير
        reports_menu = menubar.addMenu(ArabicTextHelper.reshape_arabic_text("التقارير"))
        
        financial_report_action = QAction(ArabicTextHelper.reshape_arabic_text("التقرير المالي"), self)
        financial_report_action.triggered.connect(self.financial_report)
        reports_menu.addAction(financial_report_action)
        
        # قائمة الإعدادات
        settings_menu = menubar.addMenu(ArabicTextHelper.reshape_arabic_text("الإعدادات"))
        
        preferences_action = QAction(ArabicTextHelper.reshape_arabic_text("التفضيلات"), self)
        preferences_action.triggered.connect(self.show_preferences)
        settings_menu.addAction(preferences_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu(ArabicTextHelper.reshape_arabic_text("مساعدة"))
        
        about_action = QAction(ArabicTextHelper.reshape_arabic_text("حول البرنامج"), self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar = self.addToolBar(ArabicTextHelper.reshape_arabic_text("الأدوات الرئيسية"))
        toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextBesideIcon)
        
        # أزرار الأدوات
        add_shipment_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("شحنة جديدة"))
        add_shipment_btn.clicked.connect(self.add_shipment)
        toolbar.addWidget(add_shipment_btn)
        
        add_customer_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("عميل جديد"))
        add_customer_btn.clicked.connect(self.add_customer)
        toolbar.addWidget(add_customer_btn)
        
        calculate_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("حساب الجمارك"))
        calculate_btn.clicked.connect(self.calculate_customs)
        toolbar.addWidget(calculate_btn)
    
    def create_central_widget(self):
        """إنشاء الواجهة المركزية"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الترحيب
        welcome_label = QLabel(ArabicTextHelper.reshape_arabic_text("مرحباً بك في برنامج احتساب الجمارك"))
        welcome_label.setObjectName("title_label")
        welcome_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(welcome_label)
        
        # لوحة المعلومات
        self.create_dashboard(layout)
        
        # أزرار الإجراءات السريعة
        self.create_quick_actions(layout)
    
    def create_dashboard(self, parent_layout):
        """إنشاء لوحة المعلومات"""
        dashboard_frame = QFrame()
        dashboard_frame.setObjectName("dashboard_frame")
        dashboard_frame.setFixedHeight(200)
        
        layout = QGridLayout(dashboard_frame)
        layout.setSpacing(20)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # إحصائيات سريعة
        self.create_stat_widget(layout, "إجمالي الشحنات", "0", 0, 0)
        self.create_stat_widget(layout, "الشحنات المعلقة", "0", 0, 1)
        self.create_stat_widget(layout, "إجمالي الرسوم", "0.00 USD", 0, 2)
        self.create_stat_widget(layout, "العملاء النشطون", "0", 0, 3)
        
        parent_layout.addWidget(dashboard_frame)
    
    def create_stat_widget(self, layout, title, value, row, col):
        """إنشاء ودجت الإحصائيات"""
        title_label = QLabel(ArabicTextHelper.reshape_arabic_text(title))
        title_label.setObjectName("stats_label")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        value_label = QLabel(value)
        value_label.setObjectName("title_label")
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        value_label.setStyleSheet("font-size: 24px; color: #3498db;")
        
        widget_layout = QVBoxLayout()
        widget_layout.addWidget(value_label)
        widget_layout.addWidget(title_label)
        
        widget = QWidget()
        widget.setLayout(widget_layout)
        
        layout.addWidget(widget, row, col)
    
    def create_quick_actions(self, parent_layout):
        """إنشاء أزرار الإجراءات السريعة"""
        actions_layout = QHBoxLayout()
        actions_layout.setSpacing(20)
        
        # أزرار الإجراءات
        add_shipment_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("إضافة شحنة جديدة"))
        add_shipment_btn.setFixedHeight(50)
        add_shipment_btn.clicked.connect(self.add_shipment)
        actions_layout.addWidget(add_shipment_btn)
        
        view_shipments_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("عرض جميع الشحنات"))
        view_shipments_btn.setFixedHeight(50)
        view_shipments_btn.clicked.connect(self.view_shipments)
        actions_layout.addWidget(view_shipments_btn)
        
        reports_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("التقارير والإحصائيات"))
        reports_btn.setFixedHeight(50)
        reports_btn.clicked.connect(self.financial_report)
        actions_layout.addWidget(reports_btn)
        
        settings_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("الإعدادات"))
        settings_btn.setFixedHeight(50)
        settings_btn.clicked.connect(self.show_preferences)
        actions_layout.addWidget(settings_btn)
        
        parent_layout.addLayout(actions_layout)
        parent_layout.addStretch()
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()
        status_bar.showMessage(ArabicTextHelper.reshape_arabic_text("جاهز"))
    
    # دوال الأحداث
    def new_file(self):
        """ملف جديد"""
        QMessageBox.information(self, "ملف جديد", "سيتم إضافة هذه الميزة قريباً")
    
    def open_file(self):
        """فتح ملف"""
        QMessageBox.information(self, "فتح ملف", "سيتم إضافة هذه الميزة قريباً")
    
    @handle_exception
    def add_shipment(self):
        """إضافة شحنة"""
        try:
            if self._shipment_dialog is None:
                from .shipment_window import ShipmentDialog
                self._shipment_dialog = ShipmentDialog(self)
                self._shipment_dialog.shipment_saved.connect(self.refresh_dashboard)
            self._shipment_dialog.exec()
        except ImportError as e:
            error_handler.show_error_dialog(self, "خطأ", "فشل في تحميل نافذة الشحنة", "خطأ")
        except Exception as e:
            error_handler.handle_general_error(e, "فتح نافذة إضافة الشحنة")
            error_handler.show_error_dialog(self, "خطأ", "فشل في فتح نافذة إضافة الشحنة", "خطأ")

    def view_shipments(self):
        """عرض الشحنات"""
        try:
            from .shipment_window import ShipmentListWindow
            dialog = ShipmentListWindow(self)
            dialog.exec()
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في فتح نافذة عرض الشحنات: {e}")
    
    def add_customer(self):
        """إضافة عميل"""
        QMessageBox.information(self, "إضافة عميل", "سيتم فتح نافذة إضافة العميل")
    
    def view_customers(self):
        """عرض العملاء"""
        QMessageBox.information(self, "عرض العملاء", "سيتم فتح نافذة عرض العملاء")
    
    def calculate_customs(self):
        """حساب الجمارك"""
        try:
            if self._calculator_window is None:
                from .calculator_window import CustomsCalculatorWindow
                self._calculator_window = CustomsCalculatorWindow(self)
            self._calculator_window.exec()
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في فتح حاسبة الجمارك: {e}")
    
    def financial_report(self):
        """التقرير المالي"""
        try:
            if self._reports_window is None:
                from .reports_window import ReportsWindow
                self._reports_window = ReportsWindow(self)
            self._reports_window.exec()
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في فتح نافذة التقارير: {e}")
    
    def show_preferences(self):
        """عرض الإعدادات"""
        try:
            if self._settings_window is None:
                from .settings_window import SettingsWindow
                self._settings_window = SettingsWindow(self)
            self._settings_window.exec()
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في فتح نافذة الإعدادات: {e}")
    
    def show_about(self):
        """حول البرنامج"""
        about_text = ArabicTextHelper.reshape_arabic_text(
            "برنامج احتساب الجمارك\n"
            "الإصدار 1.0\n\n"
            "برنامج متكامل لإدارة الشحنات وحساب الرسوم الجمركية\n"
            "يدعم اللغة العربية بالكامل مع واجهة احترافية"
        )
        QMessageBox.about(self, "حول البرنامج", about_text)

    @handle_exception
    def refresh_dashboard(self):
        """تحديث لوحة المعلومات"""
        session = None
        try:
            session = self.db_manager.get_session()

            # حساب الإحصائيات
            from src.database.database import Shipment, Customer

            total_shipments = session.query(Shipment).count()
            pending_shipments = session.query(Shipment).filter(Shipment.status == 'pending').count()
            active_customers = session.query(Customer).count()

            # حساب إجمالي الرسوم
            total_fees = session.query(Shipment.total_fees).all()
            total_fees_sum = sum([fee[0] for fee in total_fees if fee[0] is not None])

            # تحديث الإحصائيات في الواجهة
            # سيتم تحديث هذا عند إنشاء مراجع للعناصر

            error_handler.log_info("تم تحديث لوحة المعلومات بنجاح")

        except Exception as e:
            error_handler.handle_database_error(e, "تحديث لوحة المعلومات")
        finally:
            if session:
                session.close()
