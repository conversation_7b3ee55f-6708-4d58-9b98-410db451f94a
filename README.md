# برنامج حاسبة الجمارك - Customs Calculator

## نظرة عامة

برنامج حاسبة الجمارك هو تطبيق شامل ومتقدم لحساب رسوم الجمارك والضرائب للشحنات الدولية. تم تطوير البرنامج باستخدام Python و PySide6 مع دعم كامل للغة العربية واتجاه النص من اليمين إلى اليسار (RTL).

## الميزات الرئيسية

### 🚀 الوظائف الأساسية
- **حساب الجمارك والضرائب**: حساب دقيق للرسوم الجمركية والضرائب
- **إدارة الشحنات**: إضافة وتعديل وتتبع الشحنات
- **إدارة العملاء**: قاعدة بيانات شاملة للعملاء
- **إدارة أنواع البضائع**: تصنيف البضائع مع معدلات جمركية مختلفة

### 📊 التقارير والإحصائيات
- **تقارير مالية**: تقارير شاملة عن الإيرادات والرسوم
- **إحصائيات الشحنات**: تحليل بيانات الشحنات
- **تصدير البيانات**: تصدير التقارير بصيغ CSV و JSON

### 🎨 واجهة المستخدم
- **دعم كامل للعربية**: واجهة باللغة العربية مع دعم RTL
- **تصميم حديث**: واجهة مستخدم عصرية وسهلة الاستخدام
- **تخطيط متجاوب**: يتكيف مع أحجام الشاشات المختلفة

### ⚙️ الإعدادات والتخصيص
- **إعدادات مرنة**: تخصيص معدلات الجمارك والضرائب
- **إدارة الشركة**: إعدادات معلومات الشركة
- **نسخ احتياطي**: نسخ احتياطي واستعادة البيانات

## متطلبات النظام

### متطلبات الأجهزة
- **المعالج**: Intel Core i3 أو أعلى
- **الذاكرة**: 4 GB RAM كحد أدنى (8 GB مُوصى به)
- **التخزين**: 500 MB مساحة فارغة
- **الشاشة**: دقة 1024x768 كحد أدنى

### متطلبات البرمجيات
- **نظام التشغيل**: Windows 10/11 (64-bit)
- **Python**: 3.8 أو أحدث
- **قاعدة البيانات**: SQLite (مُضمنة) أو MySQL (اختياري)

## التثبيت والإعداد

### 1. تثبيت Python
```bash
# تحميل Python من الموقع الرسمي
https://www.python.org/downloads/
```

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل البرنامج
```bash
python main.py
```

## دليل الاستخدام السريع

### بدء التشغيل
1. شغل البرنامج من خلال تشغيل `main.py`
2. ستظهر شاشة البداية مع تحميل البرنامج
3. ستفتح النافذة الرئيسية تلقائياً

### إضافة شحنة جديدة
1. اضغط على زر "شحنة جديدة" في الشريط العلوي
2. املأ بيانات الشحنة (المرسل، المستقبل، البضاعة)
3. أدخل قيمة ووزن البضاعة
4. سيتم حساب الرسوم تلقائياً
5. اضغط "حفظ" لحفظ الشحنة

### عرض التقارير
1. اذهب إلى قائمة "التقارير"
2. اختر نوع التقرير المطلوب
3. حدد الفترة الزمنية
4. اضغط "إنشاء التقرير"
5. يمكن تصدير التقرير بصيغ مختلفة

## الهيكل التقني

### بنية المشروع
```
customs_calculator/
├── src/
│   ├── database/          # قاعدة البيانات والنماذج
│   ├── ui/               # واجهات المستخدم
│   ├── utils/            # أدوات مساعدة
│   └── reports/          # نظام التقارير
├── tests/                # اختبارات البرنامج
├── docs/                 # التوثيق
└── main.py              # ملف التشغيل الرئيسي
```

### قاعدة البيانات
- **SQLite**: قاعدة بيانات محلية سريعة
- **SQLAlchemy ORM**: طبقة تجريد قاعدة البيانات
- **تحسينات الأداء**: فهارس وتحسينات للاستعلامات

### المكتبات المستخدمة
- **PySide6**: واجهة المستخدم الرسومية
- **SQLAlchemy**: إدارة قاعدة البيانات
- **arabic-reshaper**: معالجة النصوص العربية
- **python-bidi**: دعم اتجاه النص RTL

## الاختبارات

### تشغيل الاختبارات
```bash
# اختبارات الوحدة
python -m pytest tests/

# اختبارات التكامل
python tests/test_integration.py

# اختبارات قاعدة البيانات
python tests/test_database.py
```

### تغطية الاختبارات
- **اختبارات الوحدة**: 95%+ تغطية
- **اختبارات التكامل**: جميع المكونات
- **اختبارات الأداء**: قياس سرعة الاستجابة

## الدعم والمساعدة

### المشاكل الشائعة
1. **خطأ في قاعدة البيانات**: تأكد من صلاحيات الكتابة
2. **مشاكل الخطوط العربية**: تأكد من تثبيت خطوط عربية
3. **بطء في الأداء**: تحقق من مساحة القرص الصلب

### الحصول على المساعدة
- **التوثيق الفني**: راجع ملفات docs/
- **الأسئلة الشائعة**: FAQ.md
- **تقارير الأخطاء**: استخدم نظام تتبع المشاكل

## الترخيص

هذا البرنامج مطور لأغراض تعليمية وتجارية. جميع الحقوق محفوظة.

## المطورون

تم تطوير هذا البرنامج باستخدام أحدث التقنيات والممارسات في تطوير البرمجيات.

---

**ملاحظة**: هذا البرنامج يدعم اللغة العربية بالكامل مع اتجاه النص من اليمين إلى اليسار (RTL).
