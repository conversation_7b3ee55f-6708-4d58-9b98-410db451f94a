"""
اختبارات وحدة حاسبة الجمارك
Unit tests for customs calculator
"""

import unittest
import sys
import os

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from src.utils.helpers import CustomsCalculator, NumberFormatter, ArabicTextHelper

class TestCustomsCalculator(unittest.TestCase):
    """اختبارات حاسبة الجمارك"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.calculator = CustomsCalculator()
    
    def test_calculate_customs(self):
        """اختبار حساب الجمارك"""
        # اختبار القيم العادية
        result = CustomsCalculator.calculate_customs(1000, 5.0)
        self.assertEqual(result, 50.0)
        
        # اختبار القيم الصفرية
        result = CustomsCalculator.calculate_customs(0, 5.0)
        self.assertEqual(result, 0.0)
        
        # اختبار المعدل الصفري
        result = CustomsCalculator.calculate_customs(1000, 0)
        self.assertEqual(result, 0.0)
        
        # اختبار القيم السالبة
        result = CustomsCalculator.calculate_customs(-100, 5.0)
        self.assertEqual(result, 0.0)
    
    def test_calculate_tax(self):
        """اختبار حساب الضريبة"""
        # اختبار القيم العادية
        result = CustomsCalculator.calculate_tax(1000, 15.0)
        self.assertEqual(result, 150.0)
        
        # اختبار القيم الصفرية
        result = CustomsCalculator.calculate_tax(0, 15.0)
        self.assertEqual(result, 0.0)
        
        # اختبار المعدل الصفري
        result = CustomsCalculator.calculate_tax(1000, 0)
        self.assertEqual(result, 0.0)
    
    def test_calculate_total_fees(self):
        """اختبار حساب إجمالي الرسوم"""
        # اختبار القيم العادية
        result = CustomsCalculator.calculate_total_fees(1000, 5.0, 15.0)
        expected = 50.0 + 150.0  # جمارك + ضريبة
        self.assertEqual(result, expected)
        
        # اختبار القيم الصفرية
        result = CustomsCalculator.calculate_total_fees(0, 5.0, 15.0)
        self.assertEqual(result, 0.0)
    
    def test_calculate_total_cost(self):
        """اختبار حساب التكلفة الإجمالية"""
        # اختبار القيم العادية
        result = CustomsCalculator.calculate_total_cost(1000, 5.0, 15.0)
        expected = 1000 + 50.0 + 150.0  # القيمة + جمارك + ضريبة
        self.assertEqual(result, expected)
        
        # اختبار القيم الصفرية
        result = CustomsCalculator.calculate_total_cost(0, 5.0, 15.0)
        self.assertEqual(result, 0.0)

class TestNumberFormatter(unittest.TestCase):
    """اختبارات منسق الأرقام"""
    
    def test_format_currency(self):
        """اختبار تنسيق العملة"""
        # اختبار القيم العادية
        result = NumberFormatter.format_currency(1234.56, "USD")
        self.assertEqual(result, "1,234.56 USD")
        
        # اختبار القيم الصفرية
        result = NumberFormatter.format_currency(0, "USD")
        self.assertEqual(result, "0.00 USD")
        
        # اختبار القيم الكبيرة
        result = NumberFormatter.format_currency(1234567.89, "USD")
        self.assertEqual(result, "1,234,567.89 USD")
    
    def test_format_number(self):
        """اختبار تنسيق الأرقام"""
        # اختبار الأرقام الصحيحة
        result = NumberFormatter.format_number(1234)
        self.assertEqual(result, "1,234")
        
        # اختبار الأرقام العشرية
        result = NumberFormatter.format_number(1234.56)
        self.assertEqual(result, "1,234.56")
        
        # اختبار الصفر
        result = NumberFormatter.format_number(0)
        self.assertEqual(result, "0")
    
    def test_format_percentage(self):
        """اختبار تنسيق النسبة المئوية"""
        # اختبار القيم العادية
        result = NumberFormatter.format_percentage(0.15)
        self.assertEqual(result, "15.00%")
        
        # اختبار الصفر
        result = NumberFormatter.format_percentage(0)
        self.assertEqual(result, "0.00%")
        
        # اختبار القيم الكبيرة
        result = NumberFormatter.format_percentage(1.5)
        self.assertEqual(result, "150.00%")

class TestArabicTextHelper(unittest.TestCase):
    """اختبارات مساعد النص العربي"""
    
    def test_reshape_arabic_text(self):
        """اختبار إعادة تشكيل النص العربي"""
        # اختبار النص العربي
        arabic_text = "مرحبا بك"
        result = ArabicTextHelper.reshape_arabic_text(arabic_text)
        self.assertIsInstance(result, str)
        self.assertGreater(len(result), 0)
        
        # اختبار النص الإنجليزي
        english_text = "Hello World"
        result = ArabicTextHelper.reshape_arabic_text(english_text)
        self.assertEqual(result, english_text)
        
        # اختبار النص الفارغ
        result = ArabicTextHelper.reshape_arabic_text("")
        self.assertEqual(result, "")
        
        # اختبار None
        result = ArabicTextHelper.reshape_arabic_text(None)
        self.assertEqual(result, "")
    
    def test_is_arabic_text(self):
        """اختبار التحقق من النص العربي"""
        # اختبار النص العربي
        self.assertTrue(ArabicTextHelper.is_arabic_text("مرحبا"))
        
        # اختبار النص الإنجليزي
        self.assertFalse(ArabicTextHelper.is_arabic_text("Hello"))
        
        # اختبار النص المختلط
        self.assertTrue(ArabicTextHelper.is_arabic_text("مرحبا Hello"))
        
        # اختبار النص الفارغ
        self.assertFalse(ArabicTextHelper.is_arabic_text(""))
    
    def test_clean_arabic_text(self):
        """اختبار تنظيف النص العربي"""
        # اختبار النص مع مسافات إضافية
        text = "  مرحبا   بك  "
        result = ArabicTextHelper.clean_arabic_text(text)
        self.assertEqual(result, "مرحبا بك")
        
        # اختبار النص مع أحرف خاصة
        text = "مرحبا\n\t بك"
        result = ArabicTextHelper.clean_arabic_text(text)
        self.assertEqual(result, "مرحبا بك")

if __name__ == '__main__':
    # تشغيل جميع الاختبارات
    unittest.main(verbosity=2)
