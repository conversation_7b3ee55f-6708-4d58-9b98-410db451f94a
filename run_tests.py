"""
تشغيل جميع الاختبارات
Run all tests
"""

import unittest
import sys
import os

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    # البحث عن جميع ملفات الاختبار
    loader = unittest.TestLoader()
    start_dir = os.path.join(current_dir, 'tests')
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # طباعة النتائج
    print("\n" + "="*50)
    print("ملخص نتائج الاختبارات / Test Results Summary")
    print("="*50)
    print(f"إجمالي الاختبارات / Total Tests: {result.testsRun}")
    print(f"نجح / Passed: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"فشل / Failed: {len(result.failures)}")
    print(f"أخطاء / Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nالاختبارات الفاشلة / Failed Tests:")
        for test, traceback in result.failures:
            print(f"- {test}")
    
    if result.errors:
        print("\nأخطاء الاختبارات / Test Errors:")
        for test, traceback in result.errors:
            print(f"- {test}")
    
    # إرجاع True إذا نجحت جميع الاختبارات
    return len(result.failures) == 0 and len(result.errors) == 0

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
