"""
مدير الإعدادات - Settings manager
يوفر إدارة موحدة لإعدادات البرنامج
"""

import json
import os
import sys
from datetime import datetime
from typing import Any, Dict, Optional

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from src.database.database import DatabaseManager, Settings
from src.utils.error_handler import error_handler, handle_exception

class SettingsManager:
    """مدير الإعدادات - Settings manager"""
    
    # الإعدادات الافتراضية
    DEFAULT_SETTINGS = {
        # إعدادات عامة
        'app_language': 'ar',
        'app_theme': 'light',
        'auto_save': True,
        'backup_enabled': True,
        'backup_interval_days': 7,
        
        # إعدادات قاعدة البيانات
        'db_backup_path': './backups',
        'db_auto_optimize': True,
        'db_connection_timeout': 30,
        
        # إعدادات الجمارك
        'default_currency': 'USD',
        'currency_symbol': '$',
        'decimal_places': 2,
        'tax_rate': 0.0,
        'service_fee_rate': 0.05,
        'minimum_customs_value': 0.0,
        
        # إعدادات التقارير
        'report_format': 'pdf',
        'report_language': 'ar',
        'include_charts': True,
        'auto_export': False,
        
        # إعدادات الواجهة
        'window_width': 1200,
        'window_height': 800,
        'font_size': 11,
        'font_family': 'Segoe UI',
        'rtl_layout': True,
        
        # إعدادات الأمان
        'session_timeout_minutes': 60,
        'auto_logout': False,
        'password_required': False,
        
        # إعدادات الطباعة
        'printer_name': '',
        'paper_size': 'A4',
        'print_margins': '20,20,20,20',
        'print_header': True,
        'print_footer': True,
        
        # إعدادات التصدير
        'export_path': './exports',
        'export_format': 'xlsx',
        'include_metadata': True,
        
        # إعدادات الإشعارات
        'notifications_enabled': True,
        'sound_enabled': True,
        'popup_notifications': True,
        'email_notifications': False,
        'notification_email': ''
    }
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self._cache = {}
        self._load_settings_to_cache()
    
    def _load_settings_to_cache(self):
        """تحميل الإعدادات إلى الذاكرة المؤقتة"""
        session = None
        try:
            session = self.db_manager.get_session()
            settings = session.query(Settings).all()
            
            # تحميل الإعدادات من قاعدة البيانات
            for setting in settings:
                self._cache[setting.key] = self._parse_value(setting.value)
            
            # إضافة الإعدادات الافتراضية للمفاتيح المفقودة
            for key, default_value in self.DEFAULT_SETTINGS.items():
                if key not in self._cache:
                    self._cache[key] = default_value
                    self._save_setting_to_db(key, default_value)
            
            error_handler.log_info(f"تم تحميل {len(self._cache)} إعداد")
            
        except Exception as e:
            error_handler.handle_database_error(e, "تحميل الإعدادات")
            # في حالة الخطأ، استخدم الإعدادات الافتراضية
            self._cache = self.DEFAULT_SETTINGS.copy()
        finally:
            if session:
                session.close()
    
    @handle_exception
    def get_setting(self, key: str, default: Any = None) -> Any:
        """الحصول على قيمة إعداد"""
        return self._cache.get(key, default)
    
    @handle_exception
    def set_setting(self, key: str, value: Any, category: str = 'عام') -> bool:
        """تعيين قيمة إعداد"""
        try:
            # تحديث الذاكرة المؤقتة
            self._cache[key] = value
            
            # حفظ في قاعدة البيانات
            return self._save_setting_to_db(key, value, category)
            
        except Exception as e:
            error_handler.handle_general_error(e, f"تعيين الإعداد {key}")
            return False
    
    def _save_setting_to_db(self, key: str, value: Any, category: str = 'عام') -> bool:
        """حفظ الإعداد في قاعدة البيانات"""
        session = None
        try:
            session = self.db_manager.get_session()
            
            # البحث عن الإعداد الموجود
            setting = session.query(Settings).filter(Settings.key == key).first()
            
            if setting:
                # تحديث الإعداد الموجود
                setting.value = self._serialize_value(value)
                setting.category = category
            else:
                # إنشاء إعداد جديد
                setting = Settings(
                    key=key,
                    value=self._serialize_value(value),
                    category=category,
                    description=self._get_setting_description(key)
                )
                session.add(setting)
            
            session.commit()
            error_handler.log_info(f"تم حفظ الإعداد: {key} = {value}")
            return True
            
        except Exception as e:
            if session:
                session.rollback()
            error_handler.handle_database_error(e, f"حفظ الإعداد {key}")
            return False
        finally:
            if session:
                session.close()
    
    @handle_exception
    def get_category_settings(self, category: str) -> Dict[str, Any]:
        """الحصول على جميع إعدادات فئة معينة"""
        session = None
        try:
            session = self.db_manager.get_session()
            settings = session.query(Settings).filter(Settings.category == category).all()
            
            result = {}
            for setting in settings:
                result[setting.key] = self._parse_value(setting.value)
            
            return result
            
        except Exception as e:
            error_handler.handle_database_error(e, f"الحصول على إعدادات الفئة {category}")
            return {}
        finally:
            if session:
                session.close()
    
    @handle_exception
    def reset_to_defaults(self, category: str = None) -> bool:
        """إعادة تعيين الإعدادات إلى القيم الافتراضية"""
        try:
            if category:
                # إعادة تعيين فئة معينة
                category_defaults = {k: v for k, v in self.DEFAULT_SETTINGS.items() 
                                   if self._get_setting_category(k) == category}
                for key, value in category_defaults.items():
                    self.set_setting(key, value, category)
            else:
                # إعادة تعيين جميع الإعدادات
                for key, value in self.DEFAULT_SETTINGS.items():
                    category = self._get_setting_category(key)
                    self.set_setting(key, value, category)
            
            error_handler.log_info(f"تم إعادة تعيين الإعدادات للفئة: {category or 'جميع الفئات'}")
            return True
            
        except Exception as e:
            error_handler.handle_general_error(e, "إعادة تعيين الإعدادات")
            return False
    
    @handle_exception
    def export_settings(self, filepath: str) -> bool:
        """تصدير الإعدادات إلى ملف"""
        try:
            settings_data = {
                'exported_at': str(datetime.now()),
                'version': '1.0',
                'settings': self._cache
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(settings_data, f, ensure_ascii=False, indent=2)
            
            error_handler.log_info(f"تم تصدير الإعدادات إلى: {filepath}")
            return True
            
        except Exception as e:
            error_handler.handle_file_error(e, "تصدير الإعدادات")
            return False
    
    @handle_exception
    def import_settings(self, filepath: str) -> bool:
        """استيراد الإعدادات من ملف"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                settings_data = json.load(f)
            
            if 'settings' in settings_data:
                for key, value in settings_data['settings'].items():
                    category = self._get_setting_category(key)
                    self.set_setting(key, value, category)
                
                error_handler.log_info(f"تم استيراد الإعدادات من: {filepath}")
                return True
            else:
                error_handler.log_error("ملف الإعدادات غير صالح")
                return False
                
        except Exception as e:
            error_handler.handle_file_error(e, "استيراد الإعدادات")
            return False
    
    def _serialize_value(self, value: Any) -> str:
        """تحويل القيمة إلى نص للحفظ"""
        if isinstance(value, (dict, list)):
            return json.dumps(value, ensure_ascii=False)
        return str(value)
    
    def _parse_value(self, value_str: str) -> Any:
        """تحليل القيمة من النص"""
        if not value_str:
            return None
        
        # محاولة تحليل JSON
        try:
            return json.loads(value_str)
        except:
            pass
        
        # محاولة تحويل إلى رقم
        try:
            if '.' in value_str:
                return float(value_str)
            return int(value_str)
        except:
            pass
        
        # محاولة تحويل إلى boolean
        if value_str.lower() in ('true', 'false'):
            return value_str.lower() == 'true'
        
        # إرجاع كنص
        return value_str
    
    def _get_setting_category(self, key: str) -> str:
        """الحصول على فئة الإعداد"""
        category_mapping = {
            'app_': 'عام',
            'db_': 'قاعدة البيانات',
            'default_': 'الجمارك',
            'currency': 'الجمارك',
            'tax_': 'الجمارك',
            'service_': 'الجمارك',
            'minimum_': 'الجمارك',
            'report_': 'التقارير',
            'window_': 'الواجهة',
            'font_': 'الواجهة',
            'rtl_': 'الواجهة',
            'session_': 'الأمان',
            'password_': 'الأمان',
            'auto_logout': 'الأمان',
            'printer_': 'الطباعة',
            'paper_': 'الطباعة',
            'print_': 'الطباعة',
            'export_': 'التصدير',
            'include_': 'التصدير',
            'notifications_': 'الإشعارات',
            'sound_': 'الإشعارات',
            'popup_': 'الإشعارات',
            'email_': 'الإشعارات'
        }
        
        for prefix, category in category_mapping.items():
            if key.startswith(prefix):
                return category
        
        return 'عام'
    
    def _get_setting_description(self, key: str) -> str:
        """الحصول على وصف الإعداد"""
        descriptions = {
            'app_language': 'لغة التطبيق',
            'app_theme': 'مظهر التطبيق',
            'auto_save': 'الحفظ التلقائي',
            'backup_enabled': 'تفعيل النسخ الاحتياطي',
            'backup_interval_days': 'فترة النسخ الاحتياطي بالأيام',
            'default_currency': 'العملة الافتراضية',
            'currency_symbol': 'رمز العملة',
            'decimal_places': 'عدد الخانات العشرية',
            'tax_rate': 'معدل الضريبة',
            'service_fee_rate': 'معدل رسوم الخدمة',
            'minimum_customs_value': 'الحد الأدنى لقيمة الجمارك',
            'window_width': 'عرض النافذة',
            'window_height': 'ارتفاع النافذة',
            'font_size': 'حجم الخط',
            'font_family': 'نوع الخط',
            'rtl_layout': 'التخطيط من اليمين لليسار'
        }
        
        return descriptions.get(key, f'إعداد {key}')

# إنشاء مثيل عام لمدير الإعدادات
settings_manager = SettingsManager()
