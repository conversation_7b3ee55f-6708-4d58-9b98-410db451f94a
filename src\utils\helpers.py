"""
أدوات مساعدة لبرنامج احتساب الجمارك
Helper utilities for customs calculation program
"""

import arabic_reshaper
from bidi.algorithm import get_display
from datetime import datetime
import re

class ArabicTextHelper:
    """مساعد النصوص العربية - Arabic text helper"""
    
    @staticmethod
    def reshape_arabic_text(text):
        """إعادة تشكيل النص العربي للعرض الصحيح"""
        if not text:
            return ""
        
        try:
            # إعادة تشكيل النص العربي
            reshaped_text = arabic_reshaper.reshape(str(text))
            # تطبيق خوارزمية الاتجاه الثنائي
            display_text = get_display(reshaped_text)
            return display_text
        except Exception as e:
            print(f"خطأ في تشكيل النص العربي: {e}")
            return str(text)
    
    @staticmethod
    def is_arabic_text(text):
        """التحقق من وجود نص عربي"""
        if not text:
            return False

        arabic_pattern = re.compile(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]')
        return bool(arabic_pattern.search(text))

    @staticmethod
    def clean_arabic_text(text):
        """تنظيف النص العربي من المسافات الزائدة والأحرف الخاصة"""
        if not text:
            return ""

        try:
            # إزالة المسافات الزائدة والأحرف الخاصة
            cleaned = re.sub(r'\s+', ' ', str(text).strip())
            # إزالة أحرف التحكم
            cleaned = re.sub(r'[\n\t\r]', ' ', cleaned)
            # إزالة المسافات المتعددة
            cleaned = re.sub(r' +', ' ', cleaned)
            return cleaned.strip()
        except:
            return str(text).strip()

class NumberFormatter:
    """منسق الأرقام - Number formatter"""
    
    @staticmethod
    def format_currency(amount, currency='USD', decimal_places=2):
        """تنسيق العملة"""
        try:
            formatted = f"{amount:,.{decimal_places}f}"
            return f"{formatted} {currency}"
        except:
            return f"{amount} {currency}"
    
    @staticmethod
    def format_percentage(value, decimal_places=2):
        """تنسيق النسبة المئوية"""
        try:
            percentage = value * 100
            return f"{percentage:.{decimal_places}f}%"
        except:
            return "0.00%"

    @staticmethod
    def format_number(value, decimal_places=None):
        """تنسيق الأرقام مع فواصل الآلاف"""
        try:
            if isinstance(value, int) or (isinstance(value, float) and value.is_integer()):
                return f"{int(value):,}"
            else:
                if decimal_places is None:
                    return f"{value:,.2f}"
                else:
                    return f"{value:,.{decimal_places}f}"
        except:
            return "0"
    
    @staticmethod
    def parse_number(text):
        """تحويل النص إلى رقم"""
        try:
            # إزالة الفواصل والمسافات
            cleaned = str(text).replace(',', '').replace(' ', '')
            return float(cleaned)
        except:
            return 0.0

class DateTimeHelper:
    """مساعد التاريخ والوقت - DateTime helper"""
    
    @staticmethod
    def format_date(date_obj, format_str='%Y-%m-%d'):
        """تنسيق التاريخ"""
        if not date_obj:
            return ""
        
        try:
            if isinstance(date_obj, str):
                return date_obj
            return date_obj.strftime(format_str)
        except:
            return str(date_obj)
    
    @staticmethod
    def format_datetime(datetime_obj, format_str='%Y-%m-%d %H:%M'):
        """تنسيق التاريخ والوقت"""
        if not datetime_obj:
            return ""
        
        try:
            if isinstance(datetime_obj, str):
                return datetime_obj
            return datetime_obj.strftime(format_str)
        except:
            return str(datetime_obj)
    
    @staticmethod
    def parse_date(date_str, format_str='%Y-%m-%d'):
        """تحويل النص إلى تاريخ"""
        try:
            return datetime.strptime(date_str, format_str).date()
        except:
            return None
    
    @staticmethod
    def parse_datetime(datetime_str, format_str='%Y-%m-%d %H:%M'):
        """تحويل النص إلى تاريخ ووقت"""
        try:
            return datetime.strptime(datetime_str, format_str)
        except:
            return None

class ValidationHelper:
    """مساعد التحقق من صحة البيانات - Validation helper"""
    
    @staticmethod
    def validate_email(email):
        """التحقق من صحة البريد الإلكتروني"""
        if not email:
            return False
        
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def validate_phone(phone):
        """التحقق من صحة رقم الهاتف"""
        if not phone:
            return False
        
        # إزالة المسافات والرموز
        cleaned = re.sub(r'[^\d+]', '', phone)
        # التحقق من الطول والتنسيق
        return len(cleaned) >= 7 and len(cleaned) <= 15
    
    @staticmethod
    def validate_positive_number(value):
        """التحقق من أن الرقم موجب"""
        try:
            num = float(value)
            return num >= 0
        except:
            return False
    
    @staticmethod
    def validate_required_field(value):
        """التحقق من أن الحقل مطلوب وغير فارغ"""
        if value is None:
            return False
        
        if isinstance(value, str):
            return len(value.strip()) > 0
        
        return True

class CustomsCalculator:
    """حاسبة الجمارك - Customs calculator"""
    
    @staticmethod
    def calculate_customs(value, customs_rate):
        """حساب الجمارك"""
        try:
            val = float(value)
            rate = float(customs_rate)
            if val < 0:
                return 0.0
            return (val * rate) / 100
        except:
            return 0.0
    
    @staticmethod
    def calculate_tax(value, tax_rate):
        """حساب الضريبة"""
        try:
            return (float(value) * float(tax_rate)) / 100
        except:
            return 0.0
    
    @staticmethod
    def calculate_total_fees(value, customs_rate, tax_rate):
        """حساب إجمالي الرسوم"""
        try:
            customs = CustomsCalculator.calculate_customs(value, customs_rate)
            tax = CustomsCalculator.calculate_tax(value, tax_rate)
            return customs + tax
        except:
            return 0.0
    
    @staticmethod
    def calculate_total_cost(value, customs_rate, tax_rate):
        """حساب التكلفة الإجمالية (القيمة + الرسوم)"""
        try:
            total_fees = CustomsCalculator.calculate_total_fees(value, customs_rate, tax_rate)
            return float(value) + total_fees
        except:
            return float(value) if value else 0.0

class TrackingNumberGenerator:
    """مولد أرقام التتبع - Tracking number generator"""
    
    @staticmethod
    def generate_tracking_number(prefix='CUS'):
        """توليد رقم تتبع جديد"""
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        return f"{prefix}{timestamp}"
    
    @staticmethod
    def validate_tracking_number(tracking_number):
        """التحقق من صحة رقم التتبع"""
        if not tracking_number:
            return False
        
        # التحقق من الطول والتنسيق
        return len(tracking_number) >= 5 and tracking_number.isalnum()

class FileHelper:
    """مساعد الملفات - File helper"""
    
    @staticmethod
    def get_safe_filename(filename):
        """الحصول على اسم ملف آمن"""
        # إزالة الأحرف غير المسموحة
        safe_chars = re.sub(r'[<>:"/\\|?*]', '_', filename)
        return safe_chars.strip()
    
    @staticmethod
    def get_file_extension(filename):
        """الحصول على امتداد الملف"""
        try:
            return filename.split('.')[-1].lower()
        except:
            return ""
    
    @staticmethod
    def format_file_size(size_bytes):
        """تنسيق حجم الملف"""
        try:
            if size_bytes < 1024:
                return f"{size_bytes} B"
            elif size_bytes < 1024 * 1024:
                return f"{size_bytes / 1024:.1f} KB"
            elif size_bytes < 1024 * 1024 * 1024:
                return f"{size_bytes / (1024 * 1024):.1f} MB"
            else:
                return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
        except:
            return "0 B"

class DateTimeHelper:
    """مساعد التاريخ والوقت"""

    @staticmethod
    def format_date(date_obj, format_str="%Y-%m-%d"):
        """تنسيق التاريخ"""
        if date_obj is None:
            return ""

        try:
            if hasattr(date_obj, 'strftime'):
                return date_obj.strftime(format_str)
            else:
                return str(date_obj)
        except Exception:
            return str(date_obj)

    @staticmethod
    def format_datetime(datetime_obj, format_str="%Y-%m-%d %H:%M:%S"):
        """تنسيق التاريخ والوقت"""
        if datetime_obj is None:
            return ""

        try:
            if hasattr(datetime_obj, 'strftime'):
                return datetime_obj.strftime(format_str)
            else:
                return str(datetime_obj)
        except Exception:
            return str(datetime_obj)
