"""
اختبارات التكامل النهائية لبرنامج احتساب الجمارك
Integration tests for customs calculation program
"""

import unittest
import sys
import os
from datetime import datetime, timedelta

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from src.database.database import DatabaseManager, Customer, GoodsType, Shipment, Settings
from src.utils.helpers import CustomsCalculator, NumberFormatter, ArabicTextHelper
from src.utils.error_handler import error_handler
from src.utils.settings_manager import settings_manager
from src.reports.report_generator import ReportGenerator

class IntegrationTestCase(unittest.TestCase):
    """اختبارات التكامل الأساسية"""
    
    def setUp(self):
        """إعداد الاختبار"""
        # استخدام قاعدة بيانات اختبار
        self.db_manager = DatabaseManager("test_integration.db")
        self.calculator = CustomsCalculator()
        self.number_formatter = NumberFormatter()
        self.report_generator = ReportGenerator()
        
        # إنشاء بيانات اختبار
        self.create_test_data()
    
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        # إغلاق قاعدة البيانات أولاً
        if hasattr(self, 'db_manager') and hasattr(self.db_manager, 'engine'):
            self.db_manager.engine.dispose()
        # إزالة ملف قاعدة البيانات بعد تأخير قصير
        import time
        time.sleep(0.1)
        try:
            if os.path.exists("test_integration.db"):
                os.remove("test_integration.db")
        except PermissionError:
            pass  # تجاهل الخطأ إذا كان الملف محجوز
    
    def create_test_data(self):
        """إنشاء بيانات اختبار"""
        session = self.db_manager.get_session()
        try:
            # إنشاء عميل اختبار
            customer = Customer(
                name="أحمد محمد",
                email="<EMAIL>",
                phone="123456789",
                address="الرياض، السعودية"
            )
            session.add(customer)
            
            # إنشاء نوع بضاعة اختبار
            goods_type = GoodsType(
                name="إلكترونيات",
                customs_rate=0.15,
                description="أجهزة إلكترونية"
            )
            session.add(goods_type)
            
            session.commit()
            
            # حساب الرسوم
            customs_amount = self.calculator.calculate_customs(1000.0, 15.0)  # 15% rate
            tax_amount = self.calculator.calculate_tax(1000.0, 5.0)  # 5% service fee
            total_fees = customs_amount + tax_amount

            # إنشاء شحنة اختبار
            shipment = Shipment(
                tracking_number=f"TEST{customer.id:03d}",  # رقم تتبع فريد
                sender_id=customer.id,
                receiver_id=customer.id,
                goods_type_id=goods_type.id,
                goods_description="لابتوب",
                value=1000.0,
                weight=2.5,
                customs_amount=customs_amount,
                tax_amount=tax_amount,
                total_fees=total_fees,
                status="pending",
                shipment_date=datetime.now()
            )
            
            session.add(shipment)
            session.commit()
            
            self.test_customer_id = customer.id
            self.test_goods_type_id = goods_type.id
            self.test_shipment_id = shipment.id
            
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
    
    def test_complete_workflow(self):
        """اختبار سير العمل الكامل"""
        session = self.db_manager.get_session()
        try:
            # 1. التحقق من إنشاء العميل
            customer = session.query(Customer).filter(Customer.id == self.test_customer_id).first()
            self.assertIsNotNone(customer)
            self.assertEqual(customer.name, "أحمد محمد")
            
            # 2. التحقق من إنشاء نوع البضاعة
            goods_type = session.query(GoodsType).filter(GoodsType.id == self.test_goods_type_id).first()
            self.assertIsNotNone(goods_type)
            self.assertEqual(goods_type.name, "إلكترونيات")
            self.assertEqual(goods_type.customs_rate, 0.15)
            
            # 3. التحقق من إنشاء الشحنة
            shipment = session.query(Shipment).filter(Shipment.id == self.test_shipment_id).first()
            self.assertIsNotNone(shipment)
            self.assertTrue(shipment.tracking_number.startswith("TEST"))
            self.assertEqual(shipment.value, 1000.0)
            
            # 4. التحقق من حساب الرسوم
            expected_customs = self.calculator.calculate_customs(1000.0, 15.0)  # 15% rate
            expected_service = self.calculator.calculate_tax(1000.0, 5.0)  # 5% service fee
            expected_total = expected_customs + expected_service
            
            self.assertEqual(shipment.customs_amount, expected_customs)
            self.assertEqual(shipment.tax_amount, expected_service)
            self.assertEqual(shipment.total_fees, expected_total)
            
            # 5. التحقق من العلاقات
            self.assertEqual(shipment.sender.name, "أحمد محمد")
            self.assertEqual(shipment.goods_type.name, "إلكترونيات")
            
        finally:
            session.close()
    
    def test_customs_calculation_accuracy(self):
        """اختبار دقة حساب الجمارك"""
        # اختبار قيم مختلفة
        test_cases = [
            (1000.0, 15.0, 150.0),  # قيمة عادية
            (500.0, 10.0, 50.0),    # قيمة أقل
            (0.0, 15.0, 0.0),       # قيمة صفر
            (-100.0, 15.0, 0.0),    # قيمة سالبة
        ]

        for value, rate, expected in test_cases:
            with self.subTest(value=value, rate=rate):
                result = self.calculator.calculate_customs(value, rate)
                self.assertEqual(result, expected)
    
    def test_number_formatting(self):
        """اختبار تنسيق الأرقام"""
        # اختبار تنسيق العملة
        formatted = self.number_formatter.format_currency(1000.0)
        self.assertTrue("1,000.00" in formatted)
        formatted2 = self.number_formatter.format_currency(1234.56)
        self.assertTrue("1,234.56" in formatted2)
        
        # اختبار تنسيق النسبة المئوية
        self.assertEqual(self.number_formatter.format_percentage(0.15), "15.00%")
        self.assertEqual(self.number_formatter.format_percentage(0.5), "50.00%")
    
    def test_arabic_text_processing(self):
        """اختبار معالجة النص العربي"""
        arabic_text = "برنامج احتساب الجمارك"
        processed_text = ArabicTextHelper.reshape_arabic_text(arabic_text)
        
        # التحقق من أن النص تم معالجته
        self.assertIsNotNone(processed_text)
        self.assertIsInstance(processed_text, str)
        
        # اختبار تنظيف النص
        dirty_text = "  نص عربي  \n\t  "
        cleaned_text = ArabicTextHelper.clean_arabic_text(dirty_text)
        self.assertEqual(cleaned_text, "نص عربي")
    
    def test_database_performance(self):
        """اختبار أداء قاعدة البيانات"""
        session = self.db_manager.get_session()
        try:
            # قياس وقت الاستعلام
            start_time = datetime.now()
            
            # استعلام معقد مع join صريح
            result = session.query(Shipment).join(
                Customer, Shipment.sender_id == Customer.id
            ).join(
                GoodsType, Shipment.goods_type_id == GoodsType.id
            ).filter(
                Shipment.value > 0
            ).all()
            
            end_time = datetime.now()
            query_time = (end_time - start_time).total_seconds()
            
            # التحقق من أن الاستعلام سريع (أقل من ثانية واحدة)
            self.assertLess(query_time, 1.0)
            
            # التحقق من وجود نتائج
            self.assertGreater(len(result), 0)
            
        finally:
            session.close()
    
    def test_error_handling(self):
        """اختبار معالجة الأخطاء"""
        # اختبار معالجة قيم غير صالحة
        result = self.calculator.calculate_customs(None, 15.0)
        self.assertEqual(result, 0.0)

        result = self.calculator.calculate_customs("invalid", 15.0)
        self.assertEqual(result, 0.0)

        # اختبار معالجة معدل غير صالح
        result = self.calculator.calculate_customs(1000.0, None)
        self.assertEqual(result, 0.0)

        result = self.calculator.calculate_customs(1000.0, "invalid")
        self.assertEqual(result, 0.0)
    
    def test_settings_management(self):
        """اختبار إدارة الإعدادات"""
        # اختبار تعيين واسترجاع الإعدادات
        settings_manager.set_setting("test_setting", "test_value")
        retrieved_value = settings_manager.get_setting("test_setting")
        self.assertEqual(retrieved_value, "test_value")
        
        # اختبار الإعدادات الافتراضية
        default_currency = settings_manager.get_setting("default_currency")
        self.assertEqual(default_currency, "USD")
        
        # اختبار إعدادات مختلفة الأنواع
        settings_manager.set_setting("test_number", 123)
        settings_manager.set_setting("test_boolean", True)
        settings_manager.set_setting("test_float", 12.34)
        
        self.assertEqual(settings_manager.get_setting("test_number"), 123)
        self.assertEqual(settings_manager.get_setting("test_boolean"), True)
        self.assertEqual(settings_manager.get_setting("test_float"), 12.34)
    
    def test_report_generation(self):
        """اختبار إنشاء التقارير"""
        # إنشاء ملخص الشحنات
        report = self.report_generator.generate_shipments_summary()
        self.assertIsNotNone(report)
        self.assertIn('title', report)
        self.assertIn('summary', report)
        self.assertEqual(report['title'], 'ملخص الشحنات')
        
        # إنشاء التقرير المالي
        financial_report = self.report_generator.generate_financial_report()
        self.assertIsNotNone(financial_report)
        self.assertIn('title', financial_report)
        self.assertIn('revenue_summary', financial_report)
        self.assertEqual(financial_report['title'], 'التقرير المالي')
        
        # إنشاء تحليل الجمارك
        customs_analysis = self.report_generator.generate_customs_analysis()
        self.assertIsNotNone(customs_analysis)
        self.assertIn('title', customs_analysis)
        self.assertIn('overall_statistics', customs_analysis)
        self.assertEqual(customs_analysis['title'], 'تحليل الجمارك')
    
    def test_data_integrity(self):
        """اختبار سلامة البيانات"""
        session = self.db_manager.get_session()
        try:
            # التحقق من القيود الأساسية
            shipment = session.query(Shipment).filter(Shipment.id == self.test_shipment_id).first()
            
            # التحقق من أن القيم الرقمية صحيحة
            self.assertGreaterEqual(shipment.value, 0)
            self.assertGreaterEqual(shipment.customs_amount, 0)
            self.assertGreaterEqual(shipment.tax_amount, 0)
            self.assertGreaterEqual(shipment.total_fees, 0)
            
            # التحقق من أن إجمالي الرسوم يساوي مجموع الرسوم الفردية
            expected_total = shipment.customs_amount + shipment.tax_amount
            self.assertEqual(shipment.total_fees, expected_total)
            
            # التحقق من صحة العلاقات
            self.assertIsNotNone(shipment.sender)
            self.assertIsNotNone(shipment.receiver)
            self.assertIsNotNone(shipment.goods_type)
            
        finally:
            session.close()

class PerformanceTestCase(unittest.TestCase):
    """اختبارات الأداء"""
    
    def setUp(self):
        """إعداد اختبارات الأداء"""
        self.db_manager = DatabaseManager("test_performance.db")
        self.create_large_dataset()
    
    def tearDown(self):
        """تنظيف بعد اختبارات الأداء"""
        # إغلاق قاعدة البيانات أولاً
        if hasattr(self, 'db_manager') and hasattr(self.db_manager, 'engine'):
            self.db_manager.engine.dispose()
        # إزالة ملف قاعدة البيانات بعد تأخير قصير
        import time
        time.sleep(0.1)
        try:
            if os.path.exists("test_performance.db"):
                os.remove("test_performance.db")
        except PermissionError:
            pass  # تجاهل الخطأ إذا كان الملف محجوز
    
    def create_large_dataset(self):
        """إنشاء مجموعة بيانات كبيرة للاختبار"""
        session = self.db_manager.get_session()
        try:
            # إنشاء عملاء متعددين
            customers = []
            for i in range(100):
                customer = Customer(
                    name=f"عميل {i+1}",
                    email=f"customer{i+1}@test.com",
                    phone=f"12345678{i:02d}",
                    address=f"عنوان {i+1}"
                )
                customers.append(customer)
                session.add(customer)
            
            # إنشاء أنواع بضائع متعددة
            goods_types = []
            for i in range(20):
                goods_type = GoodsType(
                    name=f"نوع بضاعة {i+1}",
                    customs_rate=0.05 + (i * 0.01),
                    description=f"وصف نوع البضاعة {i+1}"
                )
                goods_types.append(goods_type)
                session.add(goods_type)
            
            session.commit()
            
            # إنشاء شحنات متعددة
            calculator = CustomsCalculator()
            for i in range(1000):
                customer = customers[i % len(customers)]
                goods_type = goods_types[i % len(goods_types)]
                
                value = 100.0 + (i * 10.0)
                customs_fees = calculator.calculate_customs(value, goods_type.customs_rate * 100)  # Convert to percentage
                service_fees = calculator.calculate_tax(value, 5.0)  # 5% service fee
                
                # إنشاء رقم تتبع فريد
                import uuid
                unique_id = str(uuid.uuid4())[:8].upper()

                shipment = Shipment(
                    tracking_number=f"PERF{i+1:04d}_{unique_id}",
                    sender_id=customer.id,
                    receiver_id=customer.id,
                    goods_type_id=goods_type.id,
                    goods_description=f"بضاعة {i+1}",
                    value=value,
                    weight=1.0 + (i * 0.1),
                    status="delivered" if i % 3 == 0 else "pending",
                    shipment_date=datetime.now() - timedelta(days=i % 365),
                    customs_amount=customs_fees,
                    tax_amount=service_fees,
                    total_fees=customs_fees + service_fees
                )
                session.add(shipment)
                
                # حفظ كل 100 شحنة
                if (i + 1) % 100 == 0:
                    session.commit()
            
            session.commit()
            
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
    
    def test_large_query_performance(self):
        """اختبار أداء الاستعلامات الكبيرة"""
        session = self.db_manager.get_session()
        try:
            start_time = datetime.now()
            
            # استعلام معقد على بيانات كبيرة مع join صريح
            result = session.query(Shipment).join(
                Customer, Shipment.sender_id == Customer.id
            ).join(
                GoodsType, Shipment.goods_type_id == GoodsType.id
            ).filter(
                Shipment.value > 500.0,
                Shipment.status == 'delivered'
            ).order_by(Shipment.shipment_date.desc()).limit(100).all()
            
            end_time = datetime.now()
            query_time = (end_time - start_time).total_seconds()
            
            # التحقق من أن الاستعلام سريع (أقل من 3 ثوان)
            self.assertLess(query_time, 3.0)
            
            # التحقق من وجود نتائج
            self.assertGreater(len(result), 0)
            
        finally:
            session.close()
    
    def test_report_generation_performance(self):
        """اختبار أداء إنشاء التقارير"""
        report_generator = ReportGenerator()
        
        start_time = datetime.now()
        
        # إنشاء تقرير على بيانات كبيرة
        report = report_generator.generate_shipments_summary()
        
        end_time = datetime.now()
        generation_time = (end_time - start_time).total_seconds()
        
        # التحقق من أن إنشاء التقرير سريع (أقل من 5 ثوان)
        self.assertLess(generation_time, 5.0)
        
        # التحقق من صحة التقرير
        self.assertIsNotNone(report)
        self.assertIn('summary', report)

if __name__ == '__main__':
    # تشغيل جميع الاختبارات
    unittest.main(verbosity=2)
