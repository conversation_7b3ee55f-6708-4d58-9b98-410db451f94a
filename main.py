#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج احتساب الجمارك - الملف الرئيسي
Customs Calculator Program - Main File

برنامج متكامل لإدارة الشحنات وحساب الرسوم الجمركية
يدعم اللغة العربية بالكامل مع واجهة احترافية باستخدام PySide6
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from src.ui.main_window import MainWindow
from src.utils.helpers import ArabicTextHelper

class CustomsCalculatorApp:
    """تطبيق برنامج احتساب الجمارك"""
    
    def __init__(self):
        self.app = None
        self.main_window = None
        self.splash = None
    
    def create_splash_screen(self):
        """إنشاء شاشة البداية"""
        # إنشاء صورة شاشة البداية بسيطة
        pixmap = QPixmap(400, 300)
        pixmap.fill(QColor(52, 73, 94))  # لون خلفية داكن

        # إنشاء شاشة البداية
        self.splash = QSplashScreen(pixmap)
        self.splash.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.SplashScreen)

        # إضافة نص بسيط
        title_text = ArabicTextHelper.reshape_arabic_text("برنامج احتساب الجمارك")
        self.splash.showMessage(
            title_text,
            Qt.AlignmentFlag.AlignCenter,
            QColor(255, 255, 255)
        )

        return self.splash
    
    def show_splash_message(self, message):
        """عرض رسالة على شاشة البداية"""
        if self.splash:
            arabic_message = ArabicTextHelper.reshape_arabic_text(message)
            self.splash.showMessage(
                arabic_message,
                Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter,
                QColor(255, 255, 255)
            )
            self.app.processEvents()
    
    def initialize_application(self):
        """تهيئة التطبيق"""
        try:
            # إنشاء التطبيق
            self.app = QApplication(sys.argv)
            
            # إعداد خصائص التطبيق
            self.app.setApplicationName("برنامج احتساب الجمارك")
            self.app.setApplicationVersion("1.0")
            self.app.setOrganizationName("Customs Calculator")
            
            # إعداد الخط الافتراضي للعربية
            arabic_font = QFont("Segoe UI", 10)
            arabic_font.setStyleHint(QFont.StyleHint.SansSerif)
            self.app.setFont(arabic_font)
            
            # إعداد اتجاه التخطيط للعربية
            self.app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
            
            return True
            
        except Exception as e:
            print(f"خطأ في تهيئة التطبيق: {e}")
            return False
    
    def initialize_database(self):
        """تهيئة قاعدة البيانات"""
        try:
            self.show_splash_message("تهيئة قاعدة البيانات...")
            
            # سيتم إنشاء قاعدة البيانات تلقائياً عند إنشاء MainWindow
            return True
            
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {e}")
            self.show_error_message("خطأ في قاعدة البيانات", f"فشل في تهيئة قاعدة البيانات:\n{e}")
            return False
    
    def create_main_window(self):
        """إنشاء النافذة الرئيسية"""
        try:
            self.show_splash_message("تحميل الواجهة الرئيسية...")
            
            self.main_window = MainWindow()
            return True
            
        except Exception as e:
            print(f"خطأ في إنشاء النافذة الرئيسية: {e}")
            self.show_error_message("خطأ في الواجهة", f"فشل في إنشاء النافذة الرئيسية:\n{e}")
            return False
    
    def show_error_message(self, title, message):
        """عرض رسالة خطأ"""
        if self.splash:
            self.splash.hide()
        
        arabic_title = ArabicTextHelper.reshape_arabic_text(title)
        arabic_message = ArabicTextHelper.reshape_arabic_text(message)
        
        QMessageBox.critical(None, arabic_title, arabic_message)
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            # تهيئة التطبيق
            if not self.initialize_application():
                return 1
            
            # إنشاء وعرض شاشة البداية
            self.create_splash_screen()
            self.splash.show()
            
            # تأخير قصير لعرض شاشة البداية
            QTimer.singleShot(500, self.continue_initialization)
            
            # تشغيل حلقة الأحداث
            return self.app.exec()
            
        except Exception as e:
            print(f"خطأ في تشغيل التطبيق: {e}")
            return 1
    
    def continue_initialization(self):
        """متابعة التهيئة بعد عرض شاشة البداية"""
        try:
            # تهيئة قاعدة البيانات
            if not self.initialize_database():
                return
            
            # إنشاء النافذة الرئيسية
            if not self.create_main_window():
                return
            
            # إخفاء شاشة البداية وعرض النافذة الرئيسية
            QTimer.singleShot(1000, self.show_main_window)
            
        except Exception as e:
            print(f"خطأ في متابعة التهيئة: {e}")
            self.show_error_message("خطأ في التهيئة", f"فشل في تهيئة التطبيق:\n{e}")
    
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        try:
            if self.splash:
                self.splash.finish(self.main_window)
            
            if self.main_window:
                self.main_window.show()
                self.main_window.raise_()
                self.main_window.activateWindow()
            
        except Exception as e:
            print(f"خطأ في عرض النافذة الرئيسية: {e}")

def main():
    """الدالة الرئيسية"""
    try:
        # التحقق من إصدار Python
        if sys.version_info < (3, 8):
            print("يتطلب هذا البرنامج Python 3.8 أو أحدث")
            return 1
        
        # إنشاء وتشغيل التطبيق
        app = CustomsCalculatorApp()
        return app.run()
        
    except KeyboardInterrupt:
        print("\nتم إيقاف البرنامج بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"خطأ غير متوقع: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
