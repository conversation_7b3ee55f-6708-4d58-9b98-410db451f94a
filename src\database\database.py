"""
نظام إدارة قاعدة البيانات لبرنامج احتساب الجمارك
Database management system for customs calculation program
"""

from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Text, Boolean, ForeignKey, Index, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from datetime import datetime
import sys
import os

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)
import os

try:
    from src.utils.error_handler import error_handler, handle_exception
except ImportError:
    # في حالة عدم توفر معالج الأخطاء
    def handle_exception(func):
        return func
    class DummyErrorHandler:
        def handle_database_error(self, error, operation):
            print(f"Database error in {operation}: {error}")
        def log_info(self, message):
            print(f"INFO: {message}")
        def log_error(self, message):
            print(f"ERROR: {message}")
    error_handler = DummyErrorHandler()

Base = declarative_base()

class Customer(Base):
    """نموذج العملاء - Customer model"""
    __tablename__ = 'customers'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(200), nullable=False, comment='اسم العميل')
    company = Column(String(200), comment='اسم الشركة')
    phone = Column(String(20), comment='رقم الهاتف')
    email = Column(String(100), comment='البريد الإلكتروني')
    address = Column(Text, comment='العنوان')
    tax_number = Column(String(50), comment='الرقم الضريبي')
    created_at = Column(DateTime, default=datetime.now, comment='تاريخ الإنشاء')
    
    # العلاقات
    shipments_as_sender = relationship("Shipment", foreign_keys="Shipment.sender_id", back_populates="sender")
    shipments_as_receiver = relationship("Shipment", foreign_keys="Shipment.receiver_id", back_populates="receiver")

    # الفهارس
    __table_args__ = (
        Index('idx_customer_name', 'name'),
        Index('idx_customer_email', 'email'),
        Index('idx_customer_phone', 'phone'),
    )

class GoodsType(Base):
    """نموذج أنواع البضائع - Goods type model"""
    __tablename__ = 'goods_types'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(200), nullable=False, comment='اسم نوع البضاعة')
    description = Column(Text, comment='وصف البضاعة')
    customs_rate = Column(Float, default=0.0, comment='معدل الجمارك %')
    tax_rate = Column(Float, default=0.0, comment='معدل الضريبة %')
    is_active = Column(Boolean, default=True, comment='نشط/غير نشط')
    created_at = Column(DateTime, default=datetime.now, comment='تاريخ الإنشاء')
    
    # العلاقات
    shipments = relationship("Shipment", back_populates="goods_type")

    # الفهارس
    __table_args__ = (
        Index('idx_goods_type_name', 'name'),
        Index('idx_goods_type_customs_rate', 'customs_rate'),
    )

class Shipment(Base):
    """نموذج الشحنات - Shipment model"""
    __tablename__ = 'shipments'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    tracking_number = Column(String(50), unique=True, nullable=False, comment='رقم التتبع')
    
    # معلومات المرسل والمستقبل
    sender_id = Column(Integer, ForeignKey('customers.id'), comment='معرف المرسل')
    receiver_id = Column(Integer, ForeignKey('customers.id'), comment='معرف المستقبل')
    
    # معلومات البضاعة
    goods_type_id = Column(Integer, ForeignKey('goods_types.id'), comment='نوع البضاعة')
    goods_description = Column(Text, comment='وصف البضاعة')
    quantity = Column(Integer, default=1, comment='الكمية')
    weight = Column(Float, comment='الوزن (كيلو)')
    value = Column(Float, comment='قيمة البضاعة')
    currency = Column(String(3), default='USD', comment='العملة')
    
    # معلومات الشحن
    origin_country = Column(String(100), comment='بلد المنشأ')
    destination_country = Column(String(100), comment='بلد الوجهة')
    shipping_method = Column(String(50), comment='طريقة الشحن')
    
    # الحسابات
    customs_amount = Column(Float, default=0.0, comment='مبلغ الجمارك')
    tax_amount = Column(Float, default=0.0, comment='مبلغ الضريبة')
    total_fees = Column(Float, default=0.0, comment='إجمالي الرسوم')
    
    # التواريخ والحالة
    shipment_date = Column(DateTime, default=datetime.now, comment='تاريخ الشحن')
    arrival_date = Column(DateTime, comment='تاريخ الوصول')
    status = Column(String(50), default='pending', comment='حالة الشحنة')
    notes = Column(Text, comment='ملاحظات')
    created_at = Column(DateTime, default=datetime.now, comment='تاريخ الإنشاء')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='تاريخ التحديث')
    
    # العلاقات
    sender = relationship("Customer", foreign_keys=[sender_id], back_populates="shipments_as_sender")
    receiver = relationship("Customer", foreign_keys=[receiver_id], back_populates="shipments_as_receiver")
    goods_type = relationship("GoodsType", back_populates="shipments")

    # الفهارس
    __table_args__ = (
        Index('idx_shipment_tracking_number', 'tracking_number'),
        Index('idx_shipment_sender_id', 'sender_id'),
        Index('idx_shipment_receiver_id', 'receiver_id'),
        Index('idx_shipment_status', 'status'),
        Index('idx_shipment_date', 'shipment_date'),
        Index('idx_shipment_value', 'value'),
    )

class Settings(Base):
    """نموذج الإعدادات - Settings model"""
    __tablename__ = 'settings'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    key = Column(String(100), unique=True, nullable=False, comment='مفتاح الإعداد')
    value = Column(Text, comment='قيمة الإعداد')
    description = Column(Text, comment='وصف الإعداد')
    category = Column(String(50), comment='فئة الإعداد')
    created_at = Column(DateTime, default=datetime.now, comment='تاريخ الإنشاء')
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment='تاريخ التحديث')

    # الفهارس
    __table_args__ = (
        Index('idx_settings_key', 'key'),
        Index('idx_settings_category', 'category'),
    )

class DatabaseManager:
    """مدير قاعدة البيانات - Database manager"""
    
    def __init__(self, db_path="customs_calculator.db"):
        """تهيئة مدير قاعدة البيانات"""
        self.db_path = db_path
        # تحسين إعدادات SQLite للأداء
        self.engine = create_engine(
            f'sqlite:///{db_path}',
            echo=False,
            pool_pre_ping=True,
            connect_args={
                'check_same_thread': False,
                'timeout': 30
            }
        )
        self.Session = sessionmaker(bind=self.engine)
        self.create_tables()
        self.initialize_default_data()
        self.optimize_database()
    
    def create_tables(self):
        """إنشاء الجداول"""
        Base.metadata.create_all(self.engine)

    def init_database(self):
        """تهيئة قاعدة البيانات (للتوافق مع الاختبارات)"""
        self.create_tables()
        self.initialize_default_data()
    
    @handle_exception
    def get_session(self):
        """الحصول على جلسة قاعدة البيانات"""
        try:
            return self.Session()
        except Exception as e:
            error_handler.handle_database_error(e, "إنشاء جلسة قاعدة البيانات")
            raise
    
    def initialize_default_data(self):
        """تهيئة البيانات الافتراضية"""
        session = self.get_session()
        try:
            # التحقق من وجود إعدادات افتراضية
            if session.query(Settings).count() == 0:
                default_settings = [
                    Settings(key='default_currency', value='USD', description='العملة الافتراضية', category='general'),
                    Settings(key='default_customs_rate', value='5.0', description='معدل الجمارك الافتراضي %', category='customs'),
                    Settings(key='default_tax_rate', value='15.0', description='معدل الضريبة الافتراضي %', category='tax'),
                    Settings(key='company_name', value='شركة الجمارك', description='اسم الشركة', category='company'),
                    Settings(key='company_address', value='', description='عنوان الشركة', category='company'),
                ]
                
                for setting in default_settings:
                    session.add(setting)
                
                session.commit()
            
            # إضافة أنواع بضائع افتراضية
            if session.query(GoodsType).count() == 0:
                default_goods = [
                    GoodsType(name='إلكترونيات', description='أجهزة إلكترونية ومعدات', customs_rate=10.0, tax_rate=15.0),
                    GoodsType(name='ملابس', description='ملابس ومنسوجات', customs_rate=20.0, tax_rate=15.0),
                    GoodsType(name='أغذية', description='مواد غذائية', customs_rate=5.0, tax_rate=0.0),
                    GoodsType(name='كتب', description='كتب ومواد تعليمية', customs_rate=0.0, tax_rate=0.0),
                    GoodsType(name='أدوية', description='أدوية ومستلزمات طبية', customs_rate=0.0, tax_rate=0.0),
                ]
                
                for goods in default_goods:
                    session.add(goods)
                
                session.commit()
                
        except Exception as e:
            session.rollback()
            print(f"خطأ في تهيئة البيانات الافتراضية: {e}")
        finally:
            session.close()

    def optimize_database(self):
        """تحسين أداء قاعدة البيانات"""
        try:
            with self.engine.connect() as connection:
                # تفعيل WAL mode للأداء الأفضل
                connection.execute(text("PRAGMA journal_mode=WAL"))
                # تحسين cache size
                connection.execute(text("PRAGMA cache_size=10000"))
                # تحسين synchronous mode
                connection.execute(text("PRAGMA synchronous=NORMAL"))
                # تحسين temp_store
                connection.execute(text("PRAGMA temp_store=MEMORY"))
                # تحليل الجداول لتحسين الاستعلامات
                connection.execute(text("ANALYZE"))
        except Exception as e:
            print(f"خطأ في تحسين قاعدة البيانات: {e}")
