"""
اختبارات قاعدة البيانات
Database tests
"""

import unittest
import sys
import os
import tempfile
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from src.database.database import DatabaseManager, Customer, GoodsType, Shipment, Settings

class TestDatabaseManager(unittest.TestCase):
    """اختبارات مدير قاعدة البيانات"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        # إنشاء قاعدة بيانات مؤقتة للاختبار
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        
        self.db_manager = DatabaseManager(db_path=self.temp_db.name)
        self.db_manager.init_database()
    
    def tearDown(self):
        """تنظيف بعد الاختبارات"""
        # حذف قاعدة البيانات المؤقتة
        try:
            os.unlink(self.temp_db.name)
        except:
            pass
    
    def test_database_initialization(self):
        """اختبار تهيئة قاعدة البيانات"""
        # التحقق من إنشاء الجداول
        session = self.db_manager.get_session()
        try:
            # محاولة إنشاء عميل جديد
            customer = Customer(
                name="عميل تجريبي",
                email="<EMAIL>",
                phone="*********"
            )
            session.add(customer)
            session.commit()
            
            # التحقق من حفظ العميل
            saved_customer = session.query(Customer).filter_by(name="عميل تجريبي").first()
            self.assertIsNotNone(saved_customer)
            self.assertEqual(saved_customer.email, "<EMAIL>")
            
        finally:
            session.close()
    
    def test_customer_operations(self):
        """اختبار عمليات العملاء"""
        session = self.db_manager.get_session()
        try:
            # إنشاء عميل جديد
            customer = Customer(
                name="أحمد محمد",
                email="<EMAIL>",
                phone="*********",
                address="الرياض، السعودية",
                company="شركة الاختبار"
            )
            session.add(customer)
            session.commit()
            
            # البحث عن العميل
            found_customer = session.query(Customer).filter_by(name="أحمد محمد").first()
            self.assertIsNotNone(found_customer)
            self.assertEqual(found_customer.email, "<EMAIL>")
            self.assertEqual(found_customer.company, "شركة الاختبار")
            
            # تحديث العميل
            found_customer.phone = "*********"
            session.commit()
            
            # التحقق من التحديث
            updated_customer = session.query(Customer).filter_by(name="أحمد محمد").first()
            self.assertEqual(updated_customer.phone, "*********")
            
            # حذف العميل
            session.delete(updated_customer)
            session.commit()
            
            # التحقق من الحذف
            deleted_customer = session.query(Customer).filter_by(name="أحمد محمد").first()
            self.assertIsNone(deleted_customer)
            
        finally:
            session.close()
    
    def test_goods_type_operations(self):
        """اختبار عمليات أنواع البضائع"""
        session = self.db_manager.get_session()
        try:
            # إنشاء نوع بضاعة جديد
            goods_type = GoodsType(
                name="إلكترونيات",
                description="أجهزة إلكترونية متنوعة",
                customs_rate=10.0,
                tax_rate=15.0
            )
            session.add(goods_type)
            session.commit()
            
            # البحث عن نوع البضاعة
            found_goods = session.query(GoodsType).filter_by(name="إلكترونيات").first()
            self.assertIsNotNone(found_goods)
            self.assertEqual(found_goods.customs_rate, 10.0)
            self.assertEqual(found_goods.tax_rate, 15.0)
            
        finally:
            session.close()
    
    def test_shipment_operations(self):
        """اختبار عمليات الشحنات"""
        session = self.db_manager.get_session()
        try:
            # إنشاء عميل ونوع بضاعة أولاً
            sender = Customer(name="المرسل", email="<EMAIL>")
            receiver = Customer(name="المستقبل", email="<EMAIL>")
            goods_type = GoodsType(name="ملابس", customs_rate=10.0, tax_rate=15.0)
            
            session.add_all([sender, receiver, goods_type])
            session.commit()
            
            # إنشاء شحنة جديدة
            shipment = Shipment(
                tracking_number="TRK*********",
                sender_id=sender.id,
                receiver_id=receiver.id,
                goods_type_id=goods_type.id,
                goods_description="ملابس صيفية",
                value=500.0,
                currency="USD",
                weight=2.5,
                customs_amount=50.0,
                tax_amount=75.0,
                total_fees=125.0,
                status="pending",
                shipment_date=datetime.now()
            )
            session.add(shipment)
            session.commit()
            
            # البحث عن الشحنة
            found_shipment = session.query(Shipment).filter_by(tracking_number="TRK*********").first()
            self.assertIsNotNone(found_shipment)
            self.assertEqual(found_shipment.value, 500.0)
            self.assertEqual(found_shipment.status, "pending")
            self.assertEqual(found_shipment.sender.name, "المرسل")
            self.assertEqual(found_shipment.receiver.name, "المستقبل")
            
        finally:
            session.close()
    
    def test_settings_operations(self):
        """اختبار عمليات الإعدادات"""
        session = self.db_manager.get_session()
        try:
            # إنشاء إعدادات جديدة
            test_key = "test_setting_key"
            settings = Settings(
                key=test_key,
                value="قيمة اختبارية",
                description="إعداد للاختبار",
                category="test"
            )
            session.add(settings)
            session.commit()

            # البحث عن الإعدادات
            found_settings = session.query(Settings).filter_by(key=test_key).first()
            self.assertIsNotNone(found_settings)
            self.assertEqual(found_settings.value, "قيمة اختبارية")
            self.assertEqual(found_settings.category, "test")
            
        finally:
            session.close()
    
    def test_relationships(self):
        """اختبار العلاقات بين الجداول"""
        session = self.db_manager.get_session()
        try:
            # إنشاء البيانات المترابطة
            sender = Customer(name="شركة الإرسال", email="<EMAIL>")
            receiver = Customer(name="شركة الاستقبال", email="<EMAIL>")
            goods_type = GoodsType(name="معدات", customs_rate=8.0, tax_rate=15.0)
            
            session.add_all([sender, receiver, goods_type])
            session.commit()
            
            # إنشاء عدة شحنات للعميل الواحد
            for i in range(3):
                shipment = Shipment(
                    tracking_number=f"TRK{i:06d}",
                    sender_id=sender.id,
                    receiver_id=receiver.id,
                    goods_type_id=goods_type.id,
                    goods_description=f"شحنة رقم {i+1}",
                    value=100.0 * (i+1),
                    currency="USD",
                    status="pending",
                    shipment_date=datetime.now()
                )
                session.add(shipment)
            
            session.commit()
            
            # اختبار العلاقات
            sender_with_shipments = session.query(Customer).filter_by(name="شركة الإرسال").first()
            self.assertEqual(len(sender_with_shipments.shipments_as_sender), 3)

            receiver_with_shipments = session.query(Customer).filter_by(name="شركة الاستقبال").first()
            self.assertEqual(len(receiver_with_shipments.shipments_as_receiver), 3)
            
            goods_with_shipments = session.query(GoodsType).filter_by(name="معدات").first()
            self.assertEqual(len(goods_with_shipments.shipments), 3)
            
        finally:
            session.close()

if __name__ == '__main__':
    # تشغيل جميع الاختبارات
    unittest.main(verbosity=2)
