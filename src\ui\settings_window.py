"""
نافذة الإعدادات لبرنامج احتساب الجمارك
Settings window for customs calculation program
"""

import os
import sys
from datetime import datetime

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QLineEdit, QComboBox, QPushButton, 
                               QGroupBox, QCheckBox, QSpinBox, QDoubleSpinBox,
                               QTabWidget, QWidget, QTextEdit, QMessageBox,
                               QFileDialog, QColorDialog, QFontDialog)
from PySide6.QtCore import Qt, QSettings
from PySide6.QtGui import QFont, QColor

from src.utils.helpers import Arabic<PERSON><PERSON>tHel<PERSON>, NumberFormatter
from src.database.database import DatabaseManager, Settings
from src.utils.settings_manager import settings_manager
from src.utils.error_handler import error_handler, handle_exception

class SettingsWindow(QDialog):
    """نافذة الإعدادات"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.settings = QSettings("CustomsCalculator", "Settings")
        self.settings_manager = settings_manager
        self.init_ui()
        self.setup_arabic_support()
        self.load_settings()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle(ArabicTextHelper.reshape_arabic_text("الإعدادات"))
        self.setModal(True)
        self.resize(800, 600)
        
        # إعداد الخط العربي
        arabic_font = QFont("Segoe UI", 10)
        self.setFont(arabic_font)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # التبويبات
        self.create_tabs(main_layout)
        
        # أزرار الإجراءات
        self.create_action_buttons(main_layout)
        
        # تطبيق الستايل
        self.apply_styles()
    
    def setup_arabic_support(self):
        """إعداد دعم اللغة العربية"""
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    def create_tabs(self, parent_layout):
        """إنشاء التبويبات"""
        self.tabs = QTabWidget()
        
        # تبويب الإعدادات العامة
        self.create_general_settings_tab()
        
        # تبويب إعدادات الجمارك
        self.create_customs_settings_tab()
        
        # تبويب إعدادات قاعدة البيانات
        self.create_database_settings_tab()
        
        # تبويب إعدادات الواجهة
        self.create_interface_settings_tab()
        
        # تبويب إعدادات التقارير
        self.create_reports_settings_tab()
        
        parent_layout.addWidget(self.tabs)
    
    def create_general_settings_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        # معلومات الشركة
        company_group = QGroupBox(ArabicTextHelper.reshape_arabic_text("معلومات الشركة"))
        company_layout = QGridLayout(company_group)
        company_layout.setSpacing(10)
        
        # اسم الشركة
        company_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("اسم الشركة:")), 0, 0)
        self.company_name_edit = QLineEdit()
        company_layout.addWidget(self.company_name_edit, 0, 1)
        
        # عنوان الشركة
        company_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("العنوان:")), 1, 0)
        self.company_address_edit = QTextEdit()
        self.company_address_edit.setMaximumHeight(80)
        company_layout.addWidget(self.company_address_edit, 1, 1)
        
        # رقم الهاتف
        company_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("رقم الهاتف:")), 2, 0)
        self.company_phone_edit = QLineEdit()
        company_layout.addWidget(self.company_phone_edit, 2, 1)
        
        # البريد الإلكتروني
        company_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("البريد الإلكتروني:")), 3, 0)
        self.company_email_edit = QLineEdit()
        company_layout.addWidget(self.company_email_edit, 3, 1)
        
        layout.addWidget(company_group)
        
        # الإعدادات العامة
        general_group = QGroupBox(ArabicTextHelper.reshape_arabic_text("الإعدادات العامة"))
        general_layout = QGridLayout(general_group)
        general_layout.setSpacing(10)
        
        # العملة الافتراضية
        general_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("العملة الافتراضية:")), 0, 0)
        self.default_currency_combo = QComboBox()
        self.default_currency_combo.addItems(["USD", "EUR", "SAR", "AED", "EGP", "JOD"])
        general_layout.addWidget(self.default_currency_combo, 0, 1)
        
        # اللغة
        general_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("اللغة:")), 1, 0)
        self.language_combo = QComboBox()
        self.language_combo.addItems([
            ArabicTextHelper.reshape_arabic_text("العربية"),
            "English"
        ])
        general_layout.addWidget(self.language_combo, 1, 1)
        
        # حفظ تلقائي
        self.auto_save_checkbox = QCheckBox(ArabicTextHelper.reshape_arabic_text("حفظ تلقائي كل"))
        general_layout.addWidget(self.auto_save_checkbox, 2, 0)
        
        self.auto_save_interval_spin = QSpinBox()
        self.auto_save_interval_spin.setRange(1, 60)
        self.auto_save_interval_spin.setValue(5)
        self.auto_save_interval_spin.setSuffix(" دقائق")
        general_layout.addWidget(self.auto_save_interval_spin, 2, 1)
        
        layout.addWidget(general_group)
        layout.addStretch()
        
        self.tabs.addTab(tab, ArabicTextHelper.reshape_arabic_text("عام"))
    
    def create_customs_settings_tab(self):
        """إنشاء تبويب إعدادات الجمارك"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        # معدلات الجمارك الافتراضية
        customs_group = QGroupBox(ArabicTextHelper.reshape_arabic_text("معدلات الجمارك الافتراضية"))
        customs_layout = QGridLayout(customs_group)
        customs_layout.setSpacing(10)
        
        # معدل الجمارك العام
        customs_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("معدل الجمارك العام (%):")), 0, 0)
        self.default_customs_rate_spin = QDoubleSpinBox()
        self.default_customs_rate_spin.setRange(0, 100)
        self.default_customs_rate_spin.setValue(5.0)
        self.default_customs_rate_spin.setSuffix("%")
        customs_layout.addWidget(self.default_customs_rate_spin, 0, 1)
        
        # معدل ضريبة القيمة المضافة
        customs_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("ضريبة القيمة المضافة (%):")), 1, 0)
        self.default_vat_rate_spin = QDoubleSpinBox()
        self.default_vat_rate_spin.setRange(0, 100)
        self.default_vat_rate_spin.setValue(15.0)
        self.default_vat_rate_spin.setSuffix("%")
        customs_layout.addWidget(self.default_vat_rate_spin, 1, 1)
        
        # الحد الأدنى للإعفاء
        customs_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("الحد الأدنى للإعفاء:")), 2, 0)
        self.exemption_threshold_spin = QDoubleSpinBox()
        self.exemption_threshold_spin.setRange(0, 10000)
        self.exemption_threshold_spin.setValue(200.0)
        customs_layout.addWidget(self.exemption_threshold_spin, 2, 1)
        
        layout.addWidget(customs_group)
        
        # إعدادات الحساب
        calculation_group = QGroupBox(ArabicTextHelper.reshape_arabic_text("إعدادات الحساب"))
        calculation_layout = QGridLayout(calculation_group)
        calculation_layout.setSpacing(10)
        
        # طريقة التقريب
        calculation_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("طريقة التقريب:")), 0, 0)
        self.rounding_method_combo = QComboBox()
        self.rounding_method_combo.addItems([
            ArabicTextHelper.reshape_arabic_text("تقريب عادي"),
            ArabicTextHelper.reshape_arabic_text("تقريب لأعلى"),
            ArabicTextHelper.reshape_arabic_text("تقريب لأسفل")
        ])
        calculation_layout.addWidget(self.rounding_method_combo, 0, 1)
        
        # عدد المنازل العشرية
        calculation_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("عدد المنازل العشرية:")), 1, 0)
        self.decimal_places_spin = QSpinBox()
        self.decimal_places_spin.setRange(0, 6)
        self.decimal_places_spin.setValue(2)
        calculation_layout.addWidget(self.decimal_places_spin, 1, 1)
        
        layout.addWidget(calculation_group)
        layout.addStretch()
        
        self.tabs.addTab(tab, ArabicTextHelper.reshape_arabic_text("الجمارك"))
    
    def create_database_settings_tab(self):
        """إنشاء تبويب إعدادات قاعدة البيانات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        # إعدادات قاعدة البيانات
        db_group = QGroupBox(ArabicTextHelper.reshape_arabic_text("إعدادات قاعدة البيانات"))
        db_layout = QGridLayout(db_group)
        db_layout.setSpacing(10)
        
        # نوع قاعدة البيانات
        db_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("نوع قاعدة البيانات:")), 0, 0)
        self.db_type_combo = QComboBox()
        self.db_type_combo.addItems(["SQLite", "MySQL", "PostgreSQL"])
        db_layout.addWidget(self.db_type_combo, 0, 1)
        
        # مسار قاعدة البيانات
        db_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("مسار قاعدة البيانات:")), 1, 0)
        db_path_layout = QHBoxLayout()
        self.db_path_edit = QLineEdit()
        db_path_layout.addWidget(self.db_path_edit)
        
        browse_db_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("تصفح"))
        browse_db_btn.clicked.connect(self.browse_database_path)
        db_path_layout.addWidget(browse_db_btn)
        
        db_layout.addLayout(db_path_layout, 1, 1)
        
        layout.addWidget(db_group)
        
        # النسخ الاحتياطي
        backup_group = QGroupBox(ArabicTextHelper.reshape_arabic_text("النسخ الاحتياطي"))
        backup_layout = QGridLayout(backup_group)
        backup_layout.setSpacing(10)
        
        # نسخ احتياطي تلقائي
        self.auto_backup_checkbox = QCheckBox(ArabicTextHelper.reshape_arabic_text("نسخ احتياطي تلقائي"))
        backup_layout.addWidget(self.auto_backup_checkbox, 0, 0, 1, 2)
        
        # تكرار النسخ الاحتياطي
        backup_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("تكرار النسخ الاحتياطي:")), 1, 0)
        self.backup_frequency_combo = QComboBox()
        self.backup_frequency_combo.addItems([
            ArabicTextHelper.reshape_arabic_text("يومياً"),
            ArabicTextHelper.reshape_arabic_text("أسبوعياً"),
            ArabicTextHelper.reshape_arabic_text("شهرياً")
        ])
        backup_layout.addWidget(self.backup_frequency_combo, 1, 1)
        
        # مجلد النسخ الاحتياطي
        backup_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("مجلد النسخ الاحتياطي:")), 2, 0)
        backup_path_layout = QHBoxLayout()
        self.backup_path_edit = QLineEdit()
        backup_path_layout.addWidget(self.backup_path_edit)
        
        browse_backup_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("تصفح"))
        browse_backup_btn.clicked.connect(self.browse_backup_path)
        backup_path_layout.addWidget(browse_backup_btn)
        
        backup_layout.addLayout(backup_path_layout, 2, 1)
        
        layout.addWidget(backup_group)
        layout.addStretch()
        
        self.tabs.addTab(tab, ArabicTextHelper.reshape_arabic_text("قاعدة البيانات"))
    
    def create_interface_settings_tab(self):
        """إنشاء تبويب إعدادات الواجهة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        # إعدادات المظهر
        appearance_group = QGroupBox(ArabicTextHelper.reshape_arabic_text("إعدادات المظهر"))
        appearance_layout = QGridLayout(appearance_group)
        appearance_layout.setSpacing(10)
        
        # السمة
        appearance_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("السمة:")), 0, 0)
        self.theme_combo = QComboBox()
        self.theme_combo.addItems([
            ArabicTextHelper.reshape_arabic_text("فاتح"),
            ArabicTextHelper.reshape_arabic_text("داكن"),
            ArabicTextHelper.reshape_arabic_text("تلقائي")
        ])
        appearance_layout.addWidget(self.theme_combo, 0, 1)
        
        # الخط
        appearance_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("الخط:")), 1, 0)
        font_layout = QHBoxLayout()
        self.font_label = QLabel("Segoe UI, 10pt")
        font_layout.addWidget(self.font_label)
        
        choose_font_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("اختيار الخط"))
        choose_font_btn.clicked.connect(self.choose_font)
        font_layout.addWidget(choose_font_btn)
        
        appearance_layout.addLayout(font_layout, 1, 1)
        
        layout.addWidget(appearance_group)
        
        # إعدادات النوافذ
        windows_group = QGroupBox(ArabicTextHelper.reshape_arabic_text("إعدادات النوافذ"))
        windows_layout = QGridLayout(windows_group)
        windows_layout.setSpacing(10)
        
        # تذكر موضع النوافذ
        self.remember_window_pos_checkbox = QCheckBox(ArabicTextHelper.reshape_arabic_text("تذكر موضع النوافذ"))
        windows_layout.addWidget(self.remember_window_pos_checkbox, 0, 0, 1, 2)
        
        # تذكر حجم النوافذ
        self.remember_window_size_checkbox = QCheckBox(ArabicTextHelper.reshape_arabic_text("تذكر حجم النوافذ"))
        windows_layout.addWidget(self.remember_window_size_checkbox, 1, 0, 1, 2)
        
        # إظهار شاشة البداية
        self.show_splash_checkbox = QCheckBox(ArabicTextHelper.reshape_arabic_text("إظهار شاشة البداية"))
        windows_layout.addWidget(self.show_splash_checkbox, 2, 0, 1, 2)
        
        layout.addWidget(windows_group)
        layout.addStretch()
        
        self.tabs.addTab(tab, ArabicTextHelper.reshape_arabic_text("الواجهة"))
    
    def create_reports_settings_tab(self):
        """إنشاء تبويب إعدادات التقارير"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)
        
        # إعدادات التقارير
        reports_group = QGroupBox(ArabicTextHelper.reshape_arabic_text("إعدادات التقارير"))
        reports_layout = QGridLayout(reports_group)
        reports_layout.setSpacing(10)
        
        # تنسيق التاريخ في التقارير
        reports_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("تنسيق التاريخ:")), 0, 0)
        self.date_format_combo = QComboBox()
        self.date_format_combo.addItems([
            "YYYY-MM-DD",
            "DD/MM/YYYY",
            "MM/DD/YYYY",
            "DD-MM-YYYY"
        ])
        reports_layout.addWidget(self.date_format_combo, 0, 1)
        
        # تضمين الشعار في التقارير
        self.include_logo_checkbox = QCheckBox(ArabicTextHelper.reshape_arabic_text("تضمين الشعار في التقارير"))
        reports_layout.addWidget(self.include_logo_checkbox, 1, 0, 1, 2)
        
        # مسار الشعار
        reports_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("مسار الشعار:")), 2, 0)
        logo_layout = QHBoxLayout()
        self.logo_path_edit = QLineEdit()
        logo_layout.addWidget(self.logo_path_edit)
        
        browse_logo_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("تصفح"))
        browse_logo_btn.clicked.connect(self.browse_logo_path)
        logo_layout.addWidget(browse_logo_btn)
        
        reports_layout.addLayout(logo_layout, 2, 1)
        
        layout.addWidget(reports_group)
        
        # إعدادات التصدير
        export_group = QGroupBox(ArabicTextHelper.reshape_arabic_text("إعدادات التصدير"))
        export_layout = QGridLayout(export_group)
        export_layout.setSpacing(10)
        
        # مجلد التصدير الافتراضي
        export_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("مجلد التصدير الافتراضي:")), 0, 0)
        export_path_layout = QHBoxLayout()
        self.export_path_edit = QLineEdit()
        export_path_layout.addWidget(self.export_path_edit)
        
        browse_export_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("تصفح"))
        browse_export_btn.clicked.connect(self.browse_export_path)
        export_path_layout.addWidget(browse_export_btn)
        
        export_layout.addLayout(export_path_layout, 0, 1)
        
        # تنسيق التصدير الافتراضي
        export_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("تنسيق التصدير الافتراضي:")), 1, 0)
        self.export_format_combo = QComboBox()
        self.export_format_combo.addItems(["PDF", "Excel", "CSV"])
        export_layout.addWidget(self.export_format_combo, 1, 1)
        
        layout.addWidget(export_group)
        layout.addStretch()
        
        self.tabs.addTab(tab, ArabicTextHelper.reshape_arabic_text("التقارير"))
    
    def create_action_buttons(self, parent_layout):
        """إنشاء أزرار الإجراءات"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        # زر حفظ
        save_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("حفظ"))
        save_btn.clicked.connect(self.save_settings)
        buttons_layout.addWidget(save_btn)
        
        # زر إعادة تعيين
        reset_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("إعادة تعيين"))
        reset_btn.clicked.connect(self.reset_settings)
        buttons_layout.addWidget(reset_btn)
        
        # زر استيراد
        import_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("استيراد الإعدادات"))
        import_btn.clicked.connect(self.import_settings)
        buttons_layout.addWidget(import_btn)
        
        # زر تصدير
        export_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("تصدير الإعدادات"))
        export_btn.clicked.connect(self.export_settings)
        buttons_layout.addWidget(export_btn)
        
        buttons_layout.addStretch()
        
        # زر إلغاء
        cancel_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("إلغاء"))
        cancel_btn.clicked.connect(self.close)
        buttons_layout.addWidget(cancel_btn)
        
        parent_layout.addLayout(buttons_layout)

    def apply_styles(self):
        """تطبيق الستايلات"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }

            QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                background-color: white;
            }

            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border: 2px solid #3498db;
            }

            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 100px;
            }

            QPushButton:hover {
                background-color: #2980b9;
            }

            QPushButton:pressed {
                background-color: #21618c;
            }

            QCheckBox {
                spacing: 5px;
            }

            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }

            QCheckBox::indicator:unchecked {
                border: 2px solid #bdc3c7;
                border-radius: 3px;
                background-color: white;
            }

            QCheckBox::indicator:checked {
                border: 2px solid #3498db;
                border-radius: 3px;
                background-color: #3498db;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }

            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
            }

            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }

            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        """)

    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            # تحميل إعدادات الشركة
            self.company_name_edit.setText(self.settings.value("company/name", ""))
            self.company_address_edit.setPlainText(self.settings.value("company/address", ""))
            self.company_phone_edit.setText(self.settings.value("company/phone", ""))
            self.company_email_edit.setText(self.settings.value("company/email", ""))

            # تحميل الإعدادات العامة
            self.default_currency_combo.setCurrentText(self.settings.value("general/currency", "USD"))
            self.language_combo.setCurrentIndex(int(self.settings.value("general/language", 0)))
            self.auto_save_checkbox.setChecked(self.settings.value("general/auto_save", True, type=bool))
            self.auto_save_interval_spin.setValue(int(self.settings.value("general/auto_save_interval", 5)))

            # تحميل إعدادات الجمارك
            self.default_customs_rate_spin.setValue(float(self.settings.value("customs/default_rate", 5.0)))
            self.default_vat_rate_spin.setValue(float(self.settings.value("customs/vat_rate", 15.0)))
            self.exemption_threshold_spin.setValue(float(self.settings.value("customs/exemption_threshold", 200.0)))
            self.rounding_method_combo.setCurrentIndex(int(self.settings.value("customs/rounding_method", 0)))
            self.decimal_places_spin.setValue(int(self.settings.value("customs/decimal_places", 2)))

            # تحميل إعدادات قاعدة البيانات
            self.db_type_combo.setCurrentText(self.settings.value("database/type", "SQLite"))
            self.db_path_edit.setText(self.settings.value("database/path", ""))
            self.auto_backup_checkbox.setChecked(self.settings.value("database/auto_backup", True, type=bool))
            self.backup_frequency_combo.setCurrentIndex(int(self.settings.value("database/backup_frequency", 0)))
            self.backup_path_edit.setText(self.settings.value("database/backup_path", ""))

            # تحميل إعدادات الواجهة
            self.theme_combo.setCurrentIndex(int(self.settings.value("interface/theme", 0)))
            font_family = self.settings.value("interface/font_family", "Segoe UI")
            font_size = int(self.settings.value("interface/font_size", 10))
            self.font_label.setText(f"{font_family}, {font_size}pt")
            self.remember_window_pos_checkbox.setChecked(self.settings.value("interface/remember_position", True, type=bool))
            self.remember_window_size_checkbox.setChecked(self.settings.value("interface/remember_size", True, type=bool))
            self.show_splash_checkbox.setChecked(self.settings.value("interface/show_splash", True, type=bool))

            # تحميل إعدادات التقارير
            self.date_format_combo.setCurrentText(self.settings.value("reports/date_format", "YYYY-MM-DD"))
            self.include_logo_checkbox.setChecked(self.settings.value("reports/include_logo", False, type=bool))
            self.logo_path_edit.setText(self.settings.value("reports/logo_path", ""))
            self.export_path_edit.setText(self.settings.value("reports/export_path", ""))
            self.export_format_combo.setCurrentText(self.settings.value("reports/export_format", "PDF"))

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")

    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            # حفظ إعدادات الشركة
            self.settings.setValue("company/name", self.company_name_edit.text())
            self.settings.setValue("company/address", self.company_address_edit.toPlainText())
            self.settings.setValue("company/phone", self.company_phone_edit.text())
            self.settings.setValue("company/email", self.company_email_edit.text())

            # حفظ الإعدادات العامة
            self.settings.setValue("general/currency", self.default_currency_combo.currentText())
            self.settings.setValue("general/language", self.language_combo.currentIndex())
            self.settings.setValue("general/auto_save", self.auto_save_checkbox.isChecked())
            self.settings.setValue("general/auto_save_interval", self.auto_save_interval_spin.value())

            # حفظ إعدادات الجمارك
            self.settings.setValue("customs/default_rate", self.default_customs_rate_spin.value())
            self.settings.setValue("customs/vat_rate", self.default_vat_rate_spin.value())
            self.settings.setValue("customs/exemption_threshold", self.exemption_threshold_spin.value())
            self.settings.setValue("customs/rounding_method", self.rounding_method_combo.currentIndex())
            self.settings.setValue("customs/decimal_places", self.decimal_places_spin.value())

            # حفظ إعدادات قاعدة البيانات
            self.settings.setValue("database/type", self.db_type_combo.currentText())
            self.settings.setValue("database/path", self.db_path_edit.text())
            self.settings.setValue("database/auto_backup", self.auto_backup_checkbox.isChecked())
            self.settings.setValue("database/backup_frequency", self.backup_frequency_combo.currentIndex())
            self.settings.setValue("database/backup_path", self.backup_path_edit.text())

            # حفظ إعدادات الواجهة
            self.settings.setValue("interface/theme", self.theme_combo.currentIndex())
            font_text = self.font_label.text()
            if ", " in font_text:
                font_family, font_size_text = font_text.split(", ")
                font_size = int(font_size_text.replace("pt", ""))
                self.settings.setValue("interface/font_family", font_family)
                self.settings.setValue("interface/font_size", font_size)
            self.settings.setValue("interface/remember_position", self.remember_window_pos_checkbox.isChecked())
            self.settings.setValue("interface/remember_size", self.remember_window_size_checkbox.isChecked())
            self.settings.setValue("interface/show_splash", self.show_splash_checkbox.isChecked())

            # حفظ إعدادات التقارير
            self.settings.setValue("reports/date_format", self.date_format_combo.currentText())
            self.settings.setValue("reports/include_logo", self.include_logo_checkbox.isChecked())
            self.settings.setValue("reports/logo_path", self.logo_path_edit.text())
            self.settings.setValue("reports/export_path", self.export_path_edit.text())
            self.settings.setValue("reports/export_format", self.export_format_combo.currentText())

            # حفظ في قاعدة البيانات أيضاً
            self.save_to_database()

            QMessageBox.information(self, "نجح", "تم حفظ الإعدادات بنجاح")
            self.accept()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في حفظ الإعدادات: {e}")

    def save_to_database(self):
        """حفظ الإعدادات في قاعدة البيانات"""
        session = self.db_manager.get_session()
        try:
            # البحث عن الإعدادات الموجودة أو إنشاء جديدة
            settings = session.query(Settings).first()
            if not settings:
                settings = Settings()
                session.add(settings)

            # تحديث الإعدادات
            settings.company_name = self.company_name_edit.text()
            settings.company_address = self.company_address_edit.toPlainText()
            settings.company_phone = self.company_phone_edit.text()
            settings.company_email = self.company_email_edit.text()
            settings.default_currency = self.default_currency_combo.currentText()
            settings.default_customs_rate = self.default_customs_rate_spin.value()
            settings.default_vat_rate = self.default_vat_rate_spin.value()
            settings.exemption_threshold = self.exemption_threshold_spin.value()

            session.commit()

        except Exception as e:
            session.rollback()
            print(f"خطأ في حفظ الإعدادات في قاعدة البيانات: {e}")
        finally:
            session.close()

    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(
            self,
            "تأكيد",
            "هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.settings.clear()
            self.load_settings()
            QMessageBox.information(self, "تم", "تم إعادة تعيين الإعدادات إلى القيم الافتراضية")

    def import_settings(self):
        """استيراد الإعدادات"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            ArabicTextHelper.reshape_arabic_text("استيراد الإعدادات"),
            "",
            "Settings Files (*.ini);;All Files (*)"
        )

        if file_path:
            try:
                imported_settings = QSettings(file_path, QSettings.Format.IniFormat)

                # نسخ جميع الإعدادات
                for key in imported_settings.allKeys():
                    self.settings.setValue(key, imported_settings.value(key))

                self.load_settings()
                QMessageBox.information(self, "نجح", "تم استيراد الإعدادات بنجاح")

            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في استيراد الإعدادات: {e}")

    def export_settings(self):
        """تصدير الإعدادات"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            ArabicTextHelper.reshape_arabic_text("تصدير الإعدادات"),
            f"settings_{datetime.now().strftime('%Y%m%d_%H%M%S')}.ini",
            "Settings Files (*.ini);;All Files (*)"
        )

        if file_path:
            try:
                exported_settings = QSettings(file_path, QSettings.Format.IniFormat)

                # نسخ جميع الإعدادات
                for key in self.settings.allKeys():
                    exported_settings.setValue(key, self.settings.value(key))

                exported_settings.sync()
                QMessageBox.information(self, "نجح", "تم تصدير الإعدادات بنجاح")

            except Exception as e:
                QMessageBox.warning(self, "خطأ", f"فشل في تصدير الإعدادات: {e}")

    def browse_database_path(self):
        """تصفح مسار قاعدة البيانات"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            ArabicTextHelper.reshape_arabic_text("اختيار قاعدة البيانات"),
            "",
            "Database Files (*.db *.sqlite);;All Files (*)"
        )

        if file_path:
            self.db_path_edit.setText(file_path)

    def browse_backup_path(self):
        """تصفح مجلد النسخ الاحتياطي"""
        folder_path = QFileDialog.getExistingDirectory(
            self,
            ArabicTextHelper.reshape_arabic_text("اختيار مجلد النسخ الاحتياطي")
        )

        if folder_path:
            self.backup_path_edit.setText(folder_path)

    def browse_logo_path(self):
        """تصفح مسار الشعار"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            ArabicTextHelper.reshape_arabic_text("اختيار الشعار"),
            "",
            "Image Files (*.png *.jpg *.jpeg *.bmp *.gif);;All Files (*)"
        )

        if file_path:
            self.logo_path_edit.setText(file_path)

    def browse_export_path(self):
        """تصفح مجلد التصدير"""
        folder_path = QFileDialog.getExistingDirectory(
            self,
            ArabicTextHelper.reshape_arabic_text("اختيار مجلد التصدير")
        )

        if folder_path:
            self.export_path_edit.setText(folder_path)

    def choose_font(self):
        """اختيار الخط"""
        current_font_text = self.font_label.text()
        if ", " in current_font_text:
            font_family, font_size_text = current_font_text.split(", ")
            font_size = int(font_size_text.replace("pt", ""))
            current_font = QFont(font_family, font_size)
        else:
            current_font = QFont("Segoe UI", 10)

        font, ok = QFontDialog.getFont(current_font, self)
        if ok:
            self.font_label.setText(f"{font.family()}, {font.pointSize()}pt")
