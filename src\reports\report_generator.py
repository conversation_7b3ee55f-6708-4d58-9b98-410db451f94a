"""
مولد التقارير - Report generator
يوفر إنشاء تقارير مختلفة للشحنات والجمارك
"""

from datetime import datetime, timedelta
from sqlalchemy import func, and_, or_
import json
import csv
import os
import sys

# إضافة مسار المشروع
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from src.database.database import DatabaseManager, Shipment, Customer, GoodsType
from src.utils.error_handler import error_handler, handle_exception
from src.utils.helpers import NumberFormatter, DateTimeHelper

class ReportGenerator:
    """مولد التقارير - Report generator"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.number_formatter = NumberFormatter()
        self.date_helper = DateTimeHelper()
    
    @handle_exception
    def generate_shipments_summary(self, start_date=None, end_date=None):
        """إنشاء ملخص الشحنات"""
        session = None
        try:
            session = self.db_manager.get_session()
            
            # بناء الاستعلام
            query = session.query(Shipment)
            
            if start_date:
                query = query.filter(Shipment.shipment_date >= start_date)
            if end_date:
                query = query.filter(Shipment.shipment_date <= end_date)
            
            shipments = query.all()
            
            # حساب الإحصائيات
            total_shipments = len(shipments)
            total_value = sum([s.value for s in shipments if s.value])
            total_customs = sum([s.customs_fees for s in shipments if s.customs_fees])
            total_fees = sum([s.total_fees for s in shipments if s.total_fees])
            
            # تجميع حسب الحالة
            status_counts = {}
            for shipment in shipments:
                status = shipment.status or 'غير محدد'
                status_counts[status] = status_counts.get(status, 0) + 1
            
            # تجميع حسب نوع البضاعة
            goods_stats = {}
            for shipment in shipments:
                if shipment.goods_type:
                    goods_name = shipment.goods_type.name
                    if goods_name not in goods_stats:
                        goods_stats[goods_name] = {
                            'count': 0,
                            'total_value': 0,
                            'total_customs': 0
                        }
                    goods_stats[goods_name]['count'] += 1
                    goods_stats[goods_name]['total_value'] += shipment.value or 0
                    goods_stats[goods_name]['total_customs'] += shipment.customs_fees or 0
            
            report = {
                'title': 'ملخص الشحنات',
                'period': {
                    'start_date': start_date.strftime('%Y-%m-%d') if start_date else 'غير محدد',
                    'end_date': end_date.strftime('%Y-%m-%d') if end_date else 'غير محدد'
                },
                'summary': {
                    'total_shipments': total_shipments,
                    'total_value': self.number_formatter.format_currency(total_value),
                    'total_customs': self.number_formatter.format_currency(total_customs),
                    'total_fees': self.number_formatter.format_currency(total_fees),
                    'average_value': self.number_formatter.format_currency(total_value / total_shipments if total_shipments > 0 else 0)
                },
                'status_breakdown': status_counts,
                'goods_breakdown': goods_stats,
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            error_handler.log_info(f"تم إنشاء ملخص الشحنات: {total_shipments} شحنة")
            return report
            
        except Exception as e:
            error_handler.handle_database_error(e, "إنشاء ملخص الشحنات")
            return None
        finally:
            if session:
                session.close()
    
    @handle_exception
    def generate_financial_report(self, start_date=None, end_date=None):
        """إنشاء التقرير المالي"""
        session = None
        try:
            session = self.db_manager.get_session()
            
            # بناء الاستعلام
            query = session.query(Shipment)
            
            if start_date:
                query = query.filter(Shipment.shipment_date >= start_date)
            if end_date:
                query = query.filter(Shipment.shipment_date <= end_date)
            
            shipments = query.all()
            
            # حساب الإيرادات
            total_customs_revenue = sum([s.customs_fees for s in shipments if s.customs_fees])
            total_service_fees = sum([s.service_fees for s in shipments if s.service_fees])
            total_revenue = total_customs_revenue + total_service_fees
            
            # تجميع حسب الشهر
            monthly_revenue = {}
            for shipment in shipments:
                if shipment.shipment_date:
                    month_key = shipment.shipment_date.strftime('%Y-%m')
                    if month_key not in monthly_revenue:
                        monthly_revenue[month_key] = {
                            'customs': 0,
                            'service': 0,
                            'total': 0,
                            'shipments_count': 0
                        }
                    monthly_revenue[month_key]['customs'] += shipment.customs_fees or 0
                    monthly_revenue[month_key]['service'] += shipment.service_fees or 0
                    monthly_revenue[month_key]['total'] += (shipment.customs_fees or 0) + (shipment.service_fees or 0)
                    monthly_revenue[month_key]['shipments_count'] += 1
            
            # أعلى العملاء من ناحية الإيرادات
            customer_revenue = {}
            for shipment in shipments:
                if shipment.sender:
                    customer_name = shipment.sender.name
                    revenue = (shipment.customs_fees or 0) + (shipment.service_fees or 0)
                    customer_revenue[customer_name] = customer_revenue.get(customer_name, 0) + revenue
            
            # ترتيب العملاء حسب الإيرادات
            top_customers = sorted(customer_revenue.items(), key=lambda x: x[1], reverse=True)[:10]
            
            report = {
                'title': 'التقرير المالي',
                'period': {
                    'start_date': start_date.strftime('%Y-%m-%d') if start_date else 'غير محدد',
                    'end_date': end_date.strftime('%Y-%m-%d') if end_date else 'غير محدد'
                },
                'revenue_summary': {
                    'total_customs_revenue': self.number_formatter.format_currency(total_customs_revenue),
                    'total_service_fees': self.number_formatter.format_currency(total_service_fees),
                    'total_revenue': self.number_formatter.format_currency(total_revenue),
                    'average_per_shipment': self.number_formatter.format_currency(total_revenue / len(shipments) if shipments else 0)
                },
                'monthly_breakdown': monthly_revenue,
                'top_customers': [{'name': name, 'revenue': self.number_formatter.format_currency(revenue)} for name, revenue in top_customers],
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            error_handler.log_info(f"تم إنشاء التقرير المالي: إجمالي الإيرادات {total_revenue}")
            return report
            
        except Exception as e:
            error_handler.handle_database_error(e, "إنشاء التقرير المالي")
            return None
        finally:
            if session:
                session.close()
    
    @handle_exception
    def generate_customs_analysis(self, start_date=None, end_date=None):
        """إنشاء تحليل الجمارك"""
        session = None
        try:
            session = self.db_manager.get_session()
            
            # بناء الاستعلام
            query = session.query(Shipment).join(GoodsType)
            
            if start_date:
                query = query.filter(Shipment.shipment_date >= start_date)
            if end_date:
                query = query.filter(Shipment.shipment_date <= end_date)
            
            shipments = query.all()
            
            # تحليل حسب نوع البضاعة
            goods_analysis = {}
            for shipment in shipments:
                if shipment.goods_type:
                    goods_name = shipment.goods_type.name
                    customs_rate = shipment.goods_type.customs_rate
                    
                    if goods_name not in goods_analysis:
                        goods_analysis[goods_name] = {
                            'customs_rate': customs_rate,
                            'shipments_count': 0,
                            'total_value': 0,
                            'total_customs': 0,
                            'average_customs_per_shipment': 0
                        }
                    
                    goods_analysis[goods_name]['shipments_count'] += 1
                    goods_analysis[goods_name]['total_value'] += shipment.value or 0
                    goods_analysis[goods_name]['total_customs'] += shipment.customs_fees or 0
            
            # حساب المتوسطات
            for goods_name, data in goods_analysis.items():
                if data['shipments_count'] > 0:
                    data['average_customs_per_shipment'] = data['total_customs'] / data['shipments_count']
            
            # إحصائيات عامة
            total_declared_value = sum([s.value for s in shipments if s.value])
            total_customs_collected = sum([s.customs_fees for s in shipments if s.customs_fees])
            effective_rate = (total_customs_collected / total_declared_value * 100) if total_declared_value > 0 else 0
            
            report = {
                'title': 'تحليل الجمارك',
                'period': {
                    'start_date': start_date.strftime('%Y-%m-%d') if start_date else 'غير محدد',
                    'end_date': end_date.strftime('%Y-%m-%d') if end_date else 'غير محدد'
                },
                'overall_statistics': {
                    'total_declared_value': self.number_formatter.format_currency(total_declared_value),
                    'total_customs_collected': self.number_formatter.format_currency(total_customs_collected),
                    'effective_customs_rate': f"{effective_rate:.2f}%",
                    'total_shipments': len(shipments)
                },
                'goods_analysis': goods_analysis,
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            error_handler.log_info(f"تم إنشاء تحليل الجمارك: {len(shipments)} شحنة")
            return report
            
        except Exception as e:
            error_handler.handle_database_error(e, "إنشاء تحليل الجمارك")
            return None
        finally:
            if session:
                session.close()
    
    @handle_exception
    def export_report_to_csv(self, report_data, filename):
        """تصدير التقرير إلى ملف CSV"""
        try:
            if not report_data:
                return False
            
            # إنشاء مجلد التقارير إذا لم يكن موجوداً
            reports_dir = "reports"
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)
            
            filepath = os.path.join(reports_dir, filename)
            
            with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # كتابة عنوان التقرير
                writer.writerow([report_data.get('title', 'تقرير')])
                writer.writerow([f"تاريخ الإنشاء: {report_data.get('generated_at', '')}"])
                writer.writerow([])  # سطر فارغ
                
                # كتابة الملخص
                if 'summary' in report_data:
                    writer.writerow(['الملخص'])
                    for key, value in report_data['summary'].items():
                        writer.writerow([key, value])
                    writer.writerow([])
                
                # كتابة تفاصيل إضافية حسب نوع التقرير
                if 'goods_breakdown' in report_data:
                    writer.writerow(['تفصيل حسب نوع البضاعة'])
                    writer.writerow(['نوع البضاعة', 'عدد الشحنات', 'إجمالي القيمة', 'إجمالي الجمارك'])
                    for goods_name, data in report_data['goods_breakdown'].items():
                        writer.writerow([goods_name, data['count'], data['total_value'], data['total_customs']])
            
            error_handler.log_info(f"تم تصدير التقرير إلى: {filepath}")
            return True
            
        except Exception as e:
            error_handler.handle_file_error(e, "تصدير التقرير")
            return False
    
    @handle_exception
    def export_report_to_json(self, report_data, filename):
        """تصدير التقرير إلى ملف JSON"""
        try:
            if not report_data:
                return False
            
            # إنشاء مجلد التقارير إذا لم يكن موجوداً
            reports_dir = "reports"
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)
            
            filepath = os.path.join(reports_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as jsonfile:
                json.dump(report_data, jsonfile, ensure_ascii=False, indent=2)
            
            error_handler.log_info(f"تم تصدير التقرير إلى: {filepath}")
            return True
            
        except Exception as e:
            error_handler.handle_file_error(e, "تصدير التقرير")
            return False
