"""
نافذة التقارير والإحصائيات لبرنامج احتساب الجمارك
Reports and statistics window for customs calculation program
"""

import os
import sys
from datetime import datetime, timedelta

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QComboBox, QDateEdit, QPushButton,
                               QGroupBox, QTableWidget, QTableWidgetItem,
                               QHeaderView, QMessageBox, QTabWidget,
                               QWidget, QFrame, QTextEdit, QProgressBar)
from PySide6.QtCore import Qt, QDate, QThread, Signal
from PySide6.QtGui import QFont, QPalette
# from PySide6.QtCharts import QChart, QChartView, QPieSeries, QBarSeries, QBarSet, QBarCategoryAxis, QValueAxis

from src.utils.helpers import ArabicTextHelper, NumberFormatter, DateTimeHelper
from src.database.database import DatabaseManager, Shipment, Customer, GoodsType
from src.reports.report_generator import ReportGenerator
from src.utils.error_handler import error_handler, handle_exception
from sqlalchemy import func, and_, extract

class ReportsWindow(QDialog):
    """نافذة التقارير والإحصائيات"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.report_generator = ReportGenerator()
        self.init_ui()
        self.setup_arabic_support()
        self.load_initial_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle(ArabicTextHelper.reshape_arabic_text("التقارير والإحصائيات"))
        self.setModal(True)
        self.resize(1200, 800)

        # إعداد الخط العربي
        arabic_font = QFont("Segoe UI", 10)
        self.setFont(arabic_font)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)

        # شريط الفلاتر
        self.create_filters_bar(main_layout)

        # التبويبات
        self.create_tabs(main_layout)

        # أزرار الإجراءات
        self.create_action_buttons(main_layout)

        # تطبيق الستايل
        self.apply_styles()

    def setup_arabic_support(self):
        """إعداد دعم اللغة العربية"""
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

    def create_filters_bar(self, parent_layout):
        """إنشاء شريط الفلاتر"""
        filters_group = QGroupBox(ArabicTextHelper.reshape_arabic_text("فلاتر التقرير"))
        filters_layout = QHBoxLayout(filters_group)
        filters_layout.setSpacing(15)

        # فترة التاريخ
        filters_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("من تاريخ:")))
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        filters_layout.addWidget(self.start_date)

        filters_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("إلى تاريخ:")))
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        filters_layout.addWidget(self.end_date)

        # حالة الشحنة
        filters_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("الحالة:")))
        self.status_filter = QComboBox()
        self.status_filter.addItems([
            ArabicTextHelper.reshape_arabic_text("جميع الحالات"),
            ArabicTextHelper.reshape_arabic_text("معلقة"),
            ArabicTextHelper.reshape_arabic_text("في الطريق"),
            ArabicTextHelper.reshape_arabic_text("وصلت"),
            ArabicTextHelper.reshape_arabic_text("تم التسليم"),
            ArabicTextHelper.reshape_arabic_text("ملغية")
        ])
        filters_layout.addWidget(self.status_filter)

        # زر التحديث
        refresh_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("تحديث التقرير"))
        refresh_btn.clicked.connect(self.refresh_reports)
        filters_layout.addWidget(refresh_btn)

        filters_layout.addStretch()
        parent_layout.addWidget(filters_group)

    def create_tabs(self, parent_layout):
        """إنشاء التبويبات"""
        self.tabs = QTabWidget()

        # تبويب الإحصائيات العامة
        self.create_general_stats_tab()

        # تبويب تقرير الشحنات
        self.create_shipments_report_tab()

        # تبويب تقرير العملاء
        self.create_customers_report_tab()

        # تبويب التحليلات المالية
        self.create_financial_analysis_tab()

        parent_layout.addWidget(self.tabs)

    def create_general_stats_tab(self):
        """إنشاء تبويب الإحصائيات العامة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(20)

        # بطاقات الإحصائيات
        stats_layout = QGridLayout()
        stats_layout.setSpacing(15)

        # إجمالي الشحنات
        self.total_shipments_card = self.create_stat_card("إجمالي الشحنات", "0", "#3498db")
        stats_layout.addWidget(self.total_shipments_card, 0, 0)

        # الشحنات المعلقة
        self.pending_shipments_card = self.create_stat_card("الشحنات المعلقة", "0", "#f39c12")
        stats_layout.addWidget(self.pending_shipments_card, 0, 1)

        # الشحنات المكتملة
        self.completed_shipments_card = self.create_stat_card("الشحنات المكتملة", "0", "#27ae60")
        stats_layout.addWidget(self.completed_shipments_card, 0, 2)

        # إجمالي الرسوم
        self.total_fees_card = self.create_stat_card("إجمالي الرسوم", "0 USD", "#e74c3c")
        stats_layout.addWidget(self.total_fees_card, 1, 0)

        # متوسط قيمة الشحنة
        self.avg_shipment_card = self.create_stat_card("متوسط قيمة الشحنة", "0 USD", "#9b59b6")
        stats_layout.addWidget(self.avg_shipment_card, 1, 1)

        # عدد العملاء النشطين
        self.active_customers_card = self.create_stat_card("العملاء النشطين", "0", "#1abc9c")
        stats_layout.addWidget(self.active_customers_card, 1, 2)

        layout.addLayout(stats_layout)

        # ملاحظة: الرسوم البيانية متوفرة في الإصدار المتقدم
        charts_note = QLabel(ArabicTextHelper.reshape_arabic_text("ملاحظة: الرسوم البيانية متوفرة في الإصدار المتقدم"))
        charts_note.setStyleSheet("color: #7f8c8d; font-style: italic; padding: 20px; text-align: center;")
        charts_note.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(charts_note)

        self.tabs.addTab(tab, ArabicTextHelper.reshape_arabic_text("الإحصائيات العامة"))

    def create_shipments_report_tab(self):
        """إنشاء تبويب تقرير الشحنات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # جدول الشحنات
        self.shipments_table = QTableWidget()
        self.shipments_table.setAlternatingRowColors(True)
        self.shipments_table.setSortingEnabled(True)

        headers = [
            "رقم التتبع", "المرسل", "المستقبل", "نوع البضاعة",
            "القيمة", "الجمارك", "الضريبة", "إجمالي الرسوم",
            "تاريخ الشحن", "الحالة"
        ]

        self.shipments_table.setColumnCount(len(headers))
        self.shipments_table.setHorizontalHeaderLabels([ArabicTextHelper.reshape_arabic_text(h) for h in headers])

        # تعيين عرض الأعمدة
        header = self.shipments_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers)):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)

        layout.addWidget(self.shipments_table)

        self.tabs.addTab(tab, ArabicTextHelper.reshape_arabic_text("تقرير الشحنات"))

    def create_customers_report_tab(self):
        """إنشاء تبويب تقرير العملاء"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # جدول العملاء
        self.customers_table = QTableWidget()
        self.customers_table.setAlternatingRowColors(True)
        self.customers_table.setSortingEnabled(True)

        headers = [
            "اسم العميل", "الشركة", "عدد الشحنات",
            "إجمالي القيمة", "إجمالي الرسوم", "آخر شحنة"
        ]

        self.customers_table.setColumnCount(len(headers))
        self.customers_table.setHorizontalHeaderLabels([ArabicTextHelper.reshape_arabic_text(h) for h in headers])

        # تعيين عرض الأعمدة
        header = self.customers_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(headers)):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)

        layout.addWidget(self.customers_table)

        self.tabs.addTab(tab, ArabicTextHelper.reshape_arabic_text("تقرير العملاء"))

    def create_financial_analysis_tab(self):
        """إنشاء تبويب التحليلات المالية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # ملخص مالي
        summary_group = QGroupBox(ArabicTextHelper.reshape_arabic_text("الملخص المالي"))
        summary_layout = QGridLayout(summary_group)

        # إجمالي قيمة البضائع
        summary_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("إجمالي قيمة البضائع:")), 0, 0)
        self.total_goods_value_label = QLabel("0.00 USD")
        self.total_goods_value_label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
        summary_layout.addWidget(self.total_goods_value_label, 0, 1)

        # إجمالي الجمارك
        summary_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("إجمالي الجمارك:")), 0, 2)
        self.total_customs_label = QLabel("0.00 USD")
        self.total_customs_label.setStyleSheet("font-weight: bold; color: #e74c3c; font-size: 14px;")
        summary_layout.addWidget(self.total_customs_label, 0, 3)

        # إجمالي الضرائب
        summary_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("إجمالي الضرائب:")), 1, 0)
        self.total_taxes_label = QLabel("0.00 USD")
        self.total_taxes_label.setStyleSheet("font-weight: bold; color: #e67e22; font-size: 14px;")
        summary_layout.addWidget(self.total_taxes_label, 1, 1)

        # إجمالي الرسوم
        summary_layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("إجمالي الرسوم:")), 1, 2)
        self.total_all_fees_label = QLabel("0.00 USD")
        self.total_all_fees_label.setStyleSheet("font-weight: bold; color: #c0392b; font-size: 16px;")
        summary_layout.addWidget(self.total_all_fees_label, 1, 3)

        layout.addWidget(summary_group)

        # جدول التحليل حسب نوع البضاعة
        analysis_group = QGroupBox(ArabicTextHelper.reshape_arabic_text("التحليل حسب نوع البضاعة"))
        analysis_layout = QVBoxLayout(analysis_group)

        self.goods_analysis_table = QTableWidget()
        self.goods_analysis_table.setAlternatingRowColors(True)
        self.goods_analysis_table.setSortingEnabled(True)

        analysis_headers = [
            "نوع البضاعة", "عدد الشحنات", "إجمالي القيمة",
            "إجمالي الجمارك", "إجمالي الضرائب", "متوسط الرسوم"
        ]

        self.goods_analysis_table.setColumnCount(len(analysis_headers))
        self.goods_analysis_table.setHorizontalHeaderLabels([ArabicTextHelper.reshape_arabic_text(h) for h in analysis_headers])

        # تعيين عرض الأعمدة
        header = self.goods_analysis_table.horizontalHeader()
        header.setStretchLastSection(True)
        for i in range(len(analysis_headers)):
            header.setSectionResizeMode(i, QHeaderView.ResizeMode.ResizeToContents)

        analysis_layout.addWidget(self.goods_analysis_table)
        layout.addWidget(analysis_group)

        self.tabs.addTab(tab, ArabicTextHelper.reshape_arabic_text("التحليلات المالية"))

    def create_action_buttons(self, parent_layout):
        """إنشاء أزرار الإجراءات"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # زر تصدير إلى Excel
        export_excel_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("تصدير إلى Excel"))
        export_excel_btn.clicked.connect(self.export_to_excel)
        buttons_layout.addWidget(export_excel_btn)

        # زر تصدير إلى PDF
        export_pdf_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("تصدير إلى PDF"))
        export_pdf_btn.clicked.connect(self.export_to_pdf)
        buttons_layout.addWidget(export_pdf_btn)

        # زر طباعة
        print_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("طباعة"))
        print_btn.clicked.connect(self.print_report)
        buttons_layout.addWidget(print_btn)

        buttons_layout.addStretch()

        # زر إغلاق
        close_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("إغلاق"))
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)

        parent_layout.addLayout(buttons_layout)

    def create_stat_card(self, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setFrameStyle(QFrame.Shape.Box)
        card.setStyleSheet(f"""
            QFrame {{
                border: 2px solid {color};
                border-radius: 8px;
                background-color: white;
                padding: 10px;
            }}
        """)

        layout = QVBoxLayout(card)
        layout.setSpacing(5)

        # العنوان
        title_label = QLabel(ArabicTextHelper.reshape_arabic_text(title))
        title_label.setStyleSheet(f"color: {color}; font-weight: bold; font-size: 12px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # القيمة
        value_label = QLabel(value)
        value_label.setStyleSheet(f"color: {color}; font-weight: bold; font-size: 24px;")
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(value_label)

        # حفظ مرجع للقيمة للتحديث لاحقاً
        card.value_label = value_label

        return card

    def create_pie_chart(self, title):
        """إنشاء رسم بياني دائري - متوفر في الإصدار المتقدم"""
        # سيتم إضافة الرسوم البيانية في إصدار لاحق
        return None

    def create_bar_chart(self, title):
        """إنشاء رسم بياني عمودي - متوفر في الإصدار المتقدم"""
        # سيتم إضافة الرسوم البيانية في إصدار لاحق
        return None

    def apply_styles(self):
        """تطبيق الستايلات"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }

            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #3498db;
                selection-color: white;
            }

            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #ecf0f1;
            }

            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 10px;
                border: none;
                font-weight: bold;
            }

            QComboBox, QDateEdit {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                background-color: white;
            }

            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 100px;
            }

            QPushButton:hover {
                background-color: #2980b9;
            }

            QTabWidget::pane {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
            }

            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }

            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
        """)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        self.refresh_reports()

    def refresh_reports(self):
        """تحديث جميع التقارير"""
        try:
            self.load_general_stats()
            self.load_shipments_report()
            self.load_customers_report()
            self.load_financial_analysis()
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحديث التقارير: {e}")

    def get_date_filter(self):
        """الحصول على فلتر التاريخ"""
        start_date = self.start_date.date().toPython()
        end_date = self.end_date.date().toPython()
        return start_date, end_date

    def get_status_filter(self):
        """الحصول على فلتر الحالة"""
        status_text = self.status_filter.currentText()
        status_map = {
            ArabicTextHelper.reshape_arabic_text("معلقة"): 'pending',
            ArabicTextHelper.reshape_arabic_text("في الطريق"): 'in_transit',
            ArabicTextHelper.reshape_arabic_text("وصلت"): 'arrived',
            ArabicTextHelper.reshape_arabic_text("تم التسليم"): 'delivered',
            ArabicTextHelper.reshape_arabic_text("ملغية"): 'cancelled'
        }
        return status_map.get(status_text, None)

    def load_general_stats(self):
        """تحميل الإحصائيات العامة"""
        session = self.db_manager.get_session()
        try:
            start_date, end_date = self.get_date_filter()
            status_filter = self.get_status_filter()

            # بناء الاستعلام الأساسي
            query = session.query(Shipment).filter(
                and_(
                    Shipment.shipment_date >= start_date,
                    Shipment.shipment_date <= end_date
                )
            )

            if status_filter:
                query = query.filter(Shipment.status == status_filter)

            shipments = query.all()

            # حساب الإحصائيات
            total_shipments = len(shipments)
            pending_shipments = len([s for s in shipments if s.status == 'pending'])
            completed_shipments = len([s for s in shipments if s.status == 'delivered'])

            total_fees = sum([s.total_fees or 0 for s in shipments])
            avg_shipment_value = sum([s.value or 0 for s in shipments]) / max(total_shipments, 1)

            # عدد العملاء النشطين
            active_customers = session.query(Customer).count()

            # تحديث البطاقات
            self.total_shipments_card.value_label.setText(str(total_shipments))
            self.pending_shipments_card.value_label.setText(str(pending_shipments))
            self.completed_shipments_card.value_label.setText(str(completed_shipments))
            self.total_fees_card.value_label.setText(f"{total_fees:.2f} USD")
            self.avg_shipment_card.value_label.setText(f"{avg_shipment_value:.2f} USD")
            self.active_customers_card.value_label.setText(str(active_customers))

            # تحديث الرسوم البيانية (متوفر في الإصدار المتقدم)
            # self.update_status_chart(shipments)
            # self.update_monthly_chart(session, start_date, end_date)

        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات العامة: {e}")
        finally:
            session.close()

    def update_status_chart(self, shipments):
        """تحديث رسم حالات الشحنات - متوفر في الإصدار المتقدم"""
        # سيتم إضافة الرسوم البيانية في إصدار لاحق
        pass

    def update_monthly_chart(self, session, start_date, end_date):
        """تحديث رسم الشحنات الشهرية - متوفر في الإصدار المتقدم"""
        # سيتم إضافة الرسوم البيانية في إصدار لاحق
        pass

    def load_shipments_report(self):
        """تحميل تقرير الشحنات"""
        session = self.db_manager.get_session()
        try:
            start_date, end_date = self.get_date_filter()
            status_filter = self.get_status_filter()

            # بناء الاستعلام
            query = session.query(Shipment).filter(
                and_(
                    Shipment.shipment_date >= start_date,
                    Shipment.shipment_date <= end_date
                )
            )

            if status_filter:
                query = query.filter(Shipment.status == status_filter)

            shipments = query.order_by(Shipment.shipment_date.desc()).all()

            # ملء الجدول
            self.shipments_table.setRowCount(len(shipments))

            for row, shipment in enumerate(shipments):
                # رقم التتبع
                self.shipments_table.setItem(row, 0, QTableWidgetItem(shipment.tracking_number or ""))

                # المرسل
                sender_name = shipment.sender.name if shipment.sender else "غير محدد"
                self.shipments_table.setItem(row, 1, QTableWidgetItem(sender_name))

                # المستقبل
                receiver_name = shipment.receiver.name if shipment.receiver else "غير محدد"
                self.shipments_table.setItem(row, 2, QTableWidgetItem(receiver_name))

                # نوع البضاعة
                goods_type_name = shipment.goods_type.name if shipment.goods_type else "غير محدد"
                self.shipments_table.setItem(row, 3, QTableWidgetItem(goods_type_name))

                # القيمة
                value_text = NumberFormatter.format_currency(shipment.value or 0, shipment.currency or "USD")
                self.shipments_table.setItem(row, 4, QTableWidgetItem(value_text))

                # الجمارك
                customs_text = NumberFormatter.format_currency(shipment.customs_amount or 0, shipment.currency or "USD")
                self.shipments_table.setItem(row, 5, QTableWidgetItem(customs_text))

                # الضريبة
                tax_text = NumberFormatter.format_currency(shipment.tax_amount or 0, shipment.currency or "USD")
                self.shipments_table.setItem(row, 6, QTableWidgetItem(tax_text))

                # إجمالي الرسوم
                total_text = NumberFormatter.format_currency(shipment.total_fees or 0, shipment.currency or "USD")
                self.shipments_table.setItem(row, 7, QTableWidgetItem(total_text))

                # تاريخ الشحن
                date_text = DateTimeHelper.format_date(shipment.shipment_date)
                self.shipments_table.setItem(row, 8, QTableWidgetItem(date_text))

                # الحالة
                status_map = {
                    'pending': 'معلقة',
                    'in_transit': 'في الطريق',
                    'arrived': 'وصلت',
                    'delivered': 'تم التسليم',
                    'cancelled': 'ملغية'
                }
                status_text = status_map.get(shipment.status, shipment.status)
                self.shipments_table.setItem(row, 9, QTableWidgetItem(ArabicTextHelper.reshape_arabic_text(status_text)))

        except Exception as e:
            print(f"خطأ في تحميل تقرير الشحنات: {e}")
        finally:
            session.close()

    def load_customers_report(self):
        """تحميل تقرير العملاء"""
        session = self.db_manager.get_session()
        try:
            start_date, end_date = self.get_date_filter()

            # الحصول على إحصائيات العملاء
            customer_stats = session.query(
                Customer.id,
                Customer.name,
                Customer.company,
                func.count(Shipment.id).label('shipment_count'),
                func.sum(Shipment.value).label('total_value'),
                func.sum(Shipment.total_fees).label('total_fees'),
                func.max(Shipment.shipment_date).label('last_shipment')
            ).outerjoin(Shipment).filter(
                and_(
                    Shipment.shipment_date >= start_date,
                    Shipment.shipment_date <= end_date
                )
            ).group_by(Customer.id).all()

            # ملء الجدول
            self.customers_table.setRowCount(len(customer_stats))

            for row, stats in enumerate(customer_stats):
                # اسم العميل
                self.customers_table.setItem(row, 0, QTableWidgetItem(stats.name or ""))

                # الشركة
                self.customers_table.setItem(row, 1, QTableWidgetItem(stats.company or "غير محدد"))

                # عدد الشحنات
                self.customers_table.setItem(row, 2, QTableWidgetItem(str(stats.shipment_count or 0)))

                # إجمالي القيمة
                total_value_text = NumberFormatter.format_currency(stats.total_value or 0, "USD")
                self.customers_table.setItem(row, 3, QTableWidgetItem(total_value_text))

                # إجمالي الرسوم
                total_fees_text = NumberFormatter.format_currency(stats.total_fees or 0, "USD")
                self.customers_table.setItem(row, 4, QTableWidgetItem(total_fees_text))

                # آخر شحنة
                last_shipment_text = DateTimeHelper.format_date(stats.last_shipment) if stats.last_shipment else "لا توجد"
                self.customers_table.setItem(row, 5, QTableWidgetItem(last_shipment_text))

        except Exception as e:
            print(f"خطأ في تحميل تقرير العملاء: {e}")
        finally:
            session.close()

    def load_financial_analysis(self):
        """تحميل التحليلات المالية"""
        session = self.db_manager.get_session()
        try:
            start_date, end_date = self.get_date_filter()

            # الحصول على الملخص المالي
            financial_summary = session.query(
                func.sum(Shipment.value).label('total_goods_value'),
                func.sum(Shipment.customs_amount).label('total_customs'),
                func.sum(Shipment.tax_amount).label('total_taxes'),
                func.sum(Shipment.total_fees).label('total_fees')
            ).filter(
                and_(
                    Shipment.shipment_date >= start_date,
                    Shipment.shipment_date <= end_date
                )
            ).first()

            # تحديث الملخص المالي
            self.total_goods_value_label.setText(NumberFormatter.format_currency(financial_summary.total_goods_value or 0, "USD"))
            self.total_customs_label.setText(NumberFormatter.format_currency(financial_summary.total_customs or 0, "USD"))
            self.total_taxes_label.setText(NumberFormatter.format_currency(financial_summary.total_taxes or 0, "USD"))
            self.total_all_fees_label.setText(NumberFormatter.format_currency(financial_summary.total_fees or 0, "USD"))

            # التحليل حسب نوع البضاعة
            goods_analysis = session.query(
                GoodsType.name,
                func.count(Shipment.id).label('shipment_count'),
                func.sum(Shipment.value).label('total_value'),
                func.sum(Shipment.customs_amount).label('total_customs'),
                func.sum(Shipment.tax_amount).label('total_taxes'),
                func.avg(Shipment.total_fees).label('avg_fees')
            ).join(Shipment).filter(
                and_(
                    Shipment.shipment_date >= start_date,
                    Shipment.shipment_date <= end_date
                )
            ).group_by(GoodsType.id).all()

            # ملء جدول التحليل
            self.goods_analysis_table.setRowCount(len(goods_analysis))

            for row, analysis in enumerate(goods_analysis):
                # نوع البضاعة
                self.goods_analysis_table.setItem(row, 0, QTableWidgetItem(analysis.name or ""))

                # عدد الشحنات
                self.goods_analysis_table.setItem(row, 1, QTableWidgetItem(str(analysis.shipment_count or 0)))

                # إجمالي القيمة
                total_value_text = NumberFormatter.format_currency(analysis.total_value or 0, "USD")
                self.goods_analysis_table.setItem(row, 2, QTableWidgetItem(total_value_text))

                # إجمالي الجمارك
                total_customs_text = NumberFormatter.format_currency(analysis.total_customs or 0, "USD")
                self.goods_analysis_table.setItem(row, 3, QTableWidgetItem(total_customs_text))

                # إجمالي الضرائب
                total_taxes_text = NumberFormatter.format_currency(analysis.total_taxes or 0, "USD")
                self.goods_analysis_table.setItem(row, 4, QTableWidgetItem(total_taxes_text))

                # متوسط الرسوم
                avg_fees_text = NumberFormatter.format_currency(analysis.avg_fees or 0, "USD")
                self.goods_analysis_table.setItem(row, 5, QTableWidgetItem(avg_fees_text))

        except Exception as e:
            print(f"خطأ في تحميل التحليلات المالية: {e}")
        finally:
            session.close()

    def export_to_excel(self):
        """تصدير إلى Excel"""
        QMessageBox.information(self, "تصدير", "سيتم إضافة ميزة التصدير إلى Excel قريباً")

    def export_to_pdf(self):
        """تصدير إلى PDF"""
        QMessageBox.information(self, "تصدير", "سيتم إضافة ميزة التصدير إلى PDF قريباً")

    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, "طباعة", "سيتم إضافة ميزة الطباعة قريباً")