"""
نافذة حاسبة الجمارك لبرنامج احتساب الجمارك
Customs calculator window for customs calculation program
"""

import os
import sys

# إضافة مسار المشروع إلى sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
                               QLabel, QLineEdit, QComboBox, QDoubleSpinBox,
                               QPushButton, QGroupBox, QTextEdit, QFrame,
                               QMessageBox, QTableWidget, QTableWidgetItem,
                               QHeaderView, QSpinBox)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from src.utils.helpers import Arabic<PERSON><PERSON><PERSON><PERSON>el<PERSON>, CustomsCalculator, NumberFormatter
from src.database.database import DatabaseManager, GoodsType

class CustomsCalculatorWindow(QDialog):
    """نافذة حاسبة الجمارك"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = DatabaseManager()
        self.init_ui()
        self.setup_arabic_support()
        self.load_goods_types()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle(ArabicTextHelper.reshape_arabic_text("حاسبة الجمارك"))
        self.setModal(True)
        self.resize(800, 600)
        
        # إعداد الخط العربي
        arabic_font = QFont("Segoe UI", 11)
        self.setFont(arabic_font)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # مجموعة معلومات البضاعة
        self.create_goods_info_group(main_layout)
        
        # مجموعة الحسابات
        self.create_calculations_group(main_layout)
        
        # مجموعة النتائج
        self.create_results_group(main_layout)
        
        # جدول تفاصيل الحسابات
        self.create_details_table(main_layout)
        
        # أزرار الإجراءات
        self.create_action_buttons(main_layout)
        
        # تطبيق الستايل
        self.apply_styles()
    
    def setup_arabic_support(self):
        """إعداد دعم اللغة العربية"""
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    def create_goods_info_group(self, parent_layout):
        """إنشاء مجموعة معلومات البضاعة"""
        group = QGroupBox(ArabicTextHelper.reshape_arabic_text("معلومات البضاعة"))
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # نوع البضاعة
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("نوع البضاعة:")), 0, 0)
        self.goods_type_combo = QComboBox()
        self.goods_type_combo.currentTextChanged.connect(self.on_goods_type_changed)
        layout.addWidget(self.goods_type_combo, 0, 1)
        
        # وصف البضاعة
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("وصف البضاعة:")), 0, 2)
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("وصف مختصر للبضاعة")
        layout.addWidget(self.description_edit, 0, 3)
        
        # قيمة البضاعة
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("قيمة البضاعة:")), 1, 0)
        self.value_spin = QDoubleSpinBox()
        self.value_spin.setMinimum(0.01)
        self.value_spin.setMaximum(999999.99)
        self.value_spin.setDecimals(2)
        self.value_spin.valueChanged.connect(self.calculate_all)
        layout.addWidget(self.value_spin, 1, 1)
        
        # العملة
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("العملة:")), 1, 2)
        self.currency_combo = QComboBox()
        self.currency_combo.addItems(["USD", "EUR", "SAR", "AED", "EGP", "JOD", "KWD"])
        layout.addWidget(self.currency_combo, 1, 3)
        
        # الكمية
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("الكمية:")), 2, 0)
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(9999)
        self.quantity_spin.setValue(1)
        self.quantity_spin.valueChanged.connect(self.calculate_all)
        layout.addWidget(self.quantity_spin, 2, 1)
        
        # الوزن
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("الوزن (كيلو):")), 2, 2)
        self.weight_spin = QDoubleSpinBox()
        self.weight_spin.setMinimum(0.1)
        self.weight_spin.setMaximum(9999.99)
        self.weight_spin.setDecimals(2)
        self.weight_spin.setSuffix(" كيلو")
        layout.addWidget(self.weight_spin, 2, 3)
        
        parent_layout.addWidget(group)
    
    def create_calculations_group(self, parent_layout):
        """إنشاء مجموعة الحسابات"""
        group = QGroupBox(ArabicTextHelper.reshape_arabic_text("معدلات الحساب"))
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # معدل الجمارك
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("معدل الجمارك (%):")), 0, 0)
        self.customs_rate_spin = QDoubleSpinBox()
        self.customs_rate_spin.setMinimum(0.0)
        self.customs_rate_spin.setMaximum(100.0)
        self.customs_rate_spin.setDecimals(2)
        self.customs_rate_spin.setSuffix("%")
        self.customs_rate_spin.valueChanged.connect(self.calculate_all)
        layout.addWidget(self.customs_rate_spin, 0, 1)
        
        # معدل الضريبة
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("معدل الضريبة (%):")), 0, 2)
        self.tax_rate_spin = QDoubleSpinBox()
        self.tax_rate_spin.setMinimum(0.0)
        self.tax_rate_spin.setMaximum(100.0)
        self.tax_rate_spin.setDecimals(2)
        self.tax_rate_spin.setSuffix("%")
        self.tax_rate_spin.valueChanged.connect(self.calculate_all)
        layout.addWidget(self.tax_rate_spin, 0, 3)
        
        # رسوم إضافية
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("رسوم إضافية:")), 1, 0)
        self.additional_fees_spin = QDoubleSpinBox()
        self.additional_fees_spin.setMinimum(0.0)
        self.additional_fees_spin.setMaximum(99999.99)
        self.additional_fees_spin.setDecimals(2)
        self.additional_fees_spin.valueChanged.connect(self.calculate_all)
        layout.addWidget(self.additional_fees_spin, 1, 1)
        
        # خصم
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("خصم (%):")), 1, 2)
        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setMinimum(0.0)
        self.discount_spin.setMaximum(100.0)
        self.discount_spin.setDecimals(2)
        self.discount_spin.setSuffix("%")
        self.discount_spin.valueChanged.connect(self.calculate_all)
        layout.addWidget(self.discount_spin, 1, 3)
        
        parent_layout.addWidget(group)
    
    def create_results_group(self, parent_layout):
        """إنشاء مجموعة النتائج"""
        group = QGroupBox(ArabicTextHelper.reshape_arabic_text("نتائج الحساب"))
        layout = QGridLayout(group)
        layout.setSpacing(15)
        
        # إنشاء تسميات النتائج
        results_style = "font-size: 14px; font-weight: bold; padding: 8px; border: 1px solid #bdc3c7; border-radius: 4px; background-color: #f8f9fa;"
        
        # قيمة البضاعة الإجمالية
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("قيمة البضاعة الإجمالية:")), 0, 0)
        self.total_value_label = QLabel("0.00 USD")
        self.total_value_label.setStyleSheet(results_style + " color: #2c3e50;")
        layout.addWidget(self.total_value_label, 0, 1)
        
        # مبلغ الجمارك
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("مبلغ الجمارك:")), 0, 2)
        self.customs_amount_label = QLabel("0.00 USD")
        self.customs_amount_label.setStyleSheet(results_style + " color: #e74c3c;")
        layout.addWidget(self.customs_amount_label, 0, 3)
        
        # مبلغ الضريبة
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("مبلغ الضريبة:")), 1, 0)
        self.tax_amount_label = QLabel("0.00 USD")
        self.tax_amount_label.setStyleSheet(results_style + " color: #e67e22;")
        layout.addWidget(self.tax_amount_label, 1, 1)
        
        # الرسوم الإضافية
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("الرسوم الإضافية:")), 1, 2)
        self.additional_fees_label = QLabel("0.00 USD")
        self.additional_fees_label.setStyleSheet(results_style + " color: #9b59b6;")
        layout.addWidget(self.additional_fees_label, 1, 3)
        
        # الخصم
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("مبلغ الخصم:")), 2, 0)
        self.discount_amount_label = QLabel("0.00 USD")
        self.discount_amount_label.setStyleSheet(results_style + " color: #27ae60;")
        layout.addWidget(self.discount_amount_label, 2, 1)
        
        # إجمالي الرسوم
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("إجمالي الرسوم:")), 2, 2)
        self.total_fees_label = QLabel("0.00 USD")
        self.total_fees_label.setStyleSheet(results_style + " color: #c0392b; font-size: 16px;")
        layout.addWidget(self.total_fees_label, 2, 3)
        
        # التكلفة الإجمالية
        layout.addWidget(QLabel(ArabicTextHelper.reshape_arabic_text("التكلفة الإجمالية:")), 3, 1)
        self.total_cost_label = QLabel("0.00 USD")
        self.total_cost_label.setStyleSheet(results_style + " color: #8e44ad; font-size: 18px; font-weight: bold;")
        layout.addWidget(self.total_cost_label, 3, 2)
        
        parent_layout.addWidget(group)
    
    def create_details_table(self, parent_layout):
        """إنشاء جدول تفاصيل الحسابات"""
        group = QGroupBox(ArabicTextHelper.reshape_arabic_text("تفاصيل الحسابات"))
        layout = QVBoxLayout(group)
        
        self.details_table = QTableWidget()
        self.details_table.setColumnCount(3)
        self.details_table.setHorizontalHeaderLabels([
            ArabicTextHelper.reshape_arabic_text("البند"),
            ArabicTextHelper.reshape_arabic_text("المعدل/القيمة"),
            ArabicTextHelper.reshape_arabic_text("المبلغ")
        ])
        
        # تعيين عرض الأعمدة
        header = self.details_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        
        self.details_table.setMaximumHeight(200)
        layout.addWidget(self.details_table)
        
        parent_layout.addWidget(group)
    
    def create_action_buttons(self, parent_layout):
        """إنشاء أزرار الإجراءات"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        # زر الحساب
        calculate_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("حساب"))
        calculate_btn.clicked.connect(self.calculate_all)
        calculate_btn.setDefault(True)
        buttons_layout.addWidget(calculate_btn)
        
        # زر إعادة تعيين
        reset_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("إعادة تعيين"))
        reset_btn.clicked.connect(self.reset_form)
        buttons_layout.addWidget(reset_btn)
        
        # زر حفظ كشحنة
        save_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("حفظ كشحنة"))
        save_btn.clicked.connect(self.save_as_shipment)
        buttons_layout.addWidget(save_btn)
        
        # زر طباعة
        print_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("طباعة"))
        print_btn.clicked.connect(self.print_calculation)
        buttons_layout.addWidget(print_btn)
        
        buttons_layout.addStretch()
        
        # زر إغلاق
        close_btn = QPushButton(ArabicTextHelper.reshape_arabic_text("إغلاق"))
        close_btn.clicked.connect(self.close)
        buttons_layout.addWidget(close_btn)
        
        parent_layout.addLayout(buttons_layout)
    
    def apply_styles(self):
        """تطبيق الستايلات"""
        self.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
            
            QLineEdit, QComboBox, QSpinBox, QDoubleSpinBox {
                border: 1px solid #bdc3c7;
                border-radius: 4px;
                padding: 5px;
                background-color: white;
            }
            
            QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus {
                border: 2px solid #3498db;
            }
            
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 100px;
            }
            
            QPushButton:hover {
                background-color: #2980b9;
            }
            
            QPushButton:pressed {
                background-color: #21618c;
            }
            
            QTableWidget {
                gridline-color: #bdc3c7;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #ecf0f1;
            }
            
            QHeaderView::section {
                background-color: #34495e;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
    
    def load_goods_types(self):
        """تحميل أنواع البضائع"""
        session = self.db_manager.get_session()
        try:
            goods_types = session.query(GoodsType).filter(GoodsType.is_active == True).all()
            
            self.goods_type_combo.addItem(ArabicTextHelper.reshape_arabic_text("اختر نوع البضاعة"), None)
            for goods_type in goods_types:
                self.goods_type_combo.addItem(goods_type.name, goods_type)
                
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل أنواع البضائع: {e}")
        finally:
            session.close()
    
    def on_goods_type_changed(self):
        """عند تغيير نوع البضاعة"""
        goods_type = self.goods_type_combo.currentData()
        if goods_type:
            self.customs_rate_spin.setValue(goods_type.customs_rate)
            self.tax_rate_spin.setValue(goods_type.tax_rate)
            self.calculate_all()
    
    def calculate_all(self):
        """حساب جميع القيم"""
        try:
            # الحصول على القيم
            unit_value = self.value_spin.value()
            quantity = self.quantity_spin.value()
            customs_rate = self.customs_rate_spin.value()
            tax_rate = self.tax_rate_spin.value()
            additional_fees = self.additional_fees_spin.value()
            discount_rate = self.discount_spin.value()
            currency = self.currency_combo.currentText()
            
            # حساب القيمة الإجمالية
            total_value = unit_value * quantity
            
            # حساب الجمارك والضريبة
            customs_amount = CustomsCalculator.calculate_customs(total_value, customs_rate)
            tax_amount = CustomsCalculator.calculate_tax(total_value, tax_rate)
            
            # حساب الخصم
            discount_amount = (total_value * discount_rate) / 100
            
            # حساب إجمالي الرسوم
            total_fees = customs_amount + tax_amount + additional_fees - discount_amount
            
            # حساب التكلفة الإجمالية
            total_cost = total_value + total_fees
            
            # تحديث التسميات
            self.total_value_label.setText(NumberFormatter.format_currency(total_value, currency))
            self.customs_amount_label.setText(NumberFormatter.format_currency(customs_amount, currency))
            self.tax_amount_label.setText(NumberFormatter.format_currency(tax_amount, currency))
            self.additional_fees_label.setText(NumberFormatter.format_currency(additional_fees, currency))
            self.discount_amount_label.setText(NumberFormatter.format_currency(discount_amount, currency))
            self.total_fees_label.setText(NumberFormatter.format_currency(total_fees, currency))
            self.total_cost_label.setText(NumberFormatter.format_currency(total_cost, currency))
            
            # تحديث جدول التفاصيل
            self.update_details_table()
            
        except Exception as e:
            print(f"خطأ في الحساب: {e}")
    
    def update_details_table(self):
        """تحديث جدول التفاصيل"""
        try:
            details = [
                ("قيمة الوحدة", f"{self.value_spin.value():.2f}", f"{self.value_spin.value():.2f}"),
                ("الكمية", f"{self.quantity_spin.value()}", f"{self.value_spin.value() * self.quantity_spin.value():.2f}"),
                ("معدل الجمارك", f"{self.customs_rate_spin.value():.2f}%", self.customs_amount_label.text()),
                ("معدل الضريبة", f"{self.tax_rate_spin.value():.2f}%", self.tax_amount_label.text()),
                ("رسوم إضافية", f"{self.additional_fees_spin.value():.2f}", f"{self.additional_fees_spin.value():.2f}"),
                ("خصم", f"{self.discount_spin.value():.2f}%", self.discount_amount_label.text()),
            ]
            
            self.details_table.setRowCount(len(details))
            
            for row, (item, rate, amount) in enumerate(details):
                self.details_table.setItem(row, 0, QTableWidgetItem(ArabicTextHelper.reshape_arabic_text(item)))
                self.details_table.setItem(row, 1, QTableWidgetItem(rate))
                self.details_table.setItem(row, 2, QTableWidgetItem(amount))
                
        except Exception as e:
            print(f"خطأ في تحديث جدول التفاصيل: {e}")
    
    def reset_form(self):
        """إعادة تعيين النموذج"""
        self.goods_type_combo.setCurrentIndex(0)
        self.description_edit.clear()
        self.value_spin.setValue(0.0)
        self.quantity_spin.setValue(1)
        self.weight_spin.setValue(0.0)
        self.customs_rate_spin.setValue(0.0)
        self.tax_rate_spin.setValue(0.0)
        self.additional_fees_spin.setValue(0.0)
        self.discount_spin.setValue(0.0)
        self.calculate_all()
    
    def save_as_shipment(self):
        """حفظ كشحنة جديدة"""
        try:
            from .shipment_window import ShipmentDialog
            
            # إنشاء نافذة شحنة جديدة مع البيانات المحسوبة
            dialog = ShipmentDialog(self)
            
            # ملء البيانات من الحاسبة
            dialog.goods_description_edit.setPlainText(self.description_edit.text())
            dialog.quantity_spin.setValue(self.quantity_spin.value())
            dialog.weight_spin.setValue(self.weight_spin.value())
            dialog.value_spin.setValue(self.value_spin.value())
            
            currency_index = dialog.currency_combo.findText(self.currency_combo.currentText())
            if currency_index >= 0:
                dialog.currency_combo.setCurrentIndex(currency_index)
            
            dialog.customs_rate_spin.setValue(self.customs_rate_spin.value())
            dialog.tax_rate_spin.setValue(self.tax_rate_spin.value())
            
            # تحديد نوع البضاعة
            goods_type = self.goods_type_combo.currentData()
            if goods_type:
                for i in range(dialog.goods_type_combo.count()):
                    if dialog.goods_type_combo.itemData(i) == goods_type.id:
                        dialog.goods_type_combo.setCurrentIndex(i)
                        break
            
            dialog.calculate_fees()
            dialog.exec()
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في فتح نافذة الشحنة: {e}")
    
    def print_calculation(self):
        """طباعة الحساب"""
        QMessageBox.information(self, "طباعة", "سيتم إضافة ميزة الطباعة قريباً")
